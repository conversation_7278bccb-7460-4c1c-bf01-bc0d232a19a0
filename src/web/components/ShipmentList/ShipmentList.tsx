import { Helmet } from 'react-helmet-async'
import TableHeader from '../TableHeader'
import ShipmentTable from '../ShipmentTable'
import Breadcrumbs from '../Breadcrumbs'
import ShipmentDrawer from '../ShipmentDrawer'
import { useParams } from 'react-router-dom'
import { useModal } from '../../context/modal'

type ShipmentParams = {
  workspaceId: string
  buildId: string
}

export default function ShipmentList() {
  const modal = useModal()
  const { workspaceId, buildId } = useParams<ShipmentParams>() as ShipmentParams

  return (
    <div>
      <Helmet>
        <title>Shipments | Project</title>
      </Helmet>
      <Breadcrumbs />
      <TableHeader
        label="Shipments"
        createLabel="New Shipment"
        onCreate={async () => {
          await modal.appoint(<ShipmentDrawer workspaceId={workspaceId} buildId={buildId} />)
        }}
      />
      <ShipmentTable />
    </div>
  )
}
