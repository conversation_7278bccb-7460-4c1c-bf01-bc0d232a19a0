import { useMutation, useQueryClient, useQuery } from 'react-query'

import Project<PERSON><PERSON> from '../../api'
import { Workspace } from '../../../api/types'

export function useWorkspaces() {
  return useQuery<Workspace[], Error>(['get-workspaces'], () => ProjectApi.getWorkspaces())
}

interface DeleteWorkspaceMutationProps {
  id: string
}

export function useDeleteWorkspace() {
  const queryClient = useQueryClient()
  return useMutation(
    async ({ id }: DeleteWorkspaceMutationProps) => {
      return ProjectApi.deleteWorkspace(id)
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries('get-workspaces')
        queryClient.invalidateQueries('get-workspace')
      },
    }
  )
}
