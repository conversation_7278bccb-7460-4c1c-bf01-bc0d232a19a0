import { Box } from '@mui/material'
import { useNavigate } from 'react-router-dom'

import { Workspace } from '../../../api/types'
import { Column } from '../../types/table'
import TableActions from '../TableActions'

export enum ColumnType {
  Id = 'id',
  Title = 'title',
}

const WorkspaceTitleColumn = ({ workspace }: { workspace: Workspace }) => {
  const navigate = useNavigate()
  return (
    <Box onClick={() => navigate(`/workspaces/${workspace.id}`)} style={{ cursor: 'pointer' }}>
      {workspace[ColumnType.Title]}
    </Box>
  )
}

const WorkspaceIdColumn = ({ workspace }: { workspace: Workspace }) => {
  const navigate = useNavigate()
  return (
    <Box onClick={() => navigate(`/workspaces/${workspace.id}`)} style={{ cursor: 'pointer' }}>
      {workspace[ColumnType.Id]}
    </Box>
  )
}

export const columns: Column<ColumnType, Workspace>[] = [
  {
    id: ColumnType.Id,
    label: 'ID',
    format: (workspace) => <WorkspaceIdColumn workspace={workspace} />,
    width: 360,
  },
  {
    id: ColumnType.Title,
    label: 'Title',
    format: (workspace) => <WorkspaceTitleColumn workspace={workspace} />,
  },
  {
    id: ColumnType.Id,
    width: 72,
    format: (data, { onEdit, onDelete }) => {
      const navItems: any = [
        {
          label: 'Edit',
          onClick: () => onEdit(),
        },
        {
          label: 'Delete',
          onClick: () => onDelete(),
        },
      ]
      return <TableActions items={navItems} />
    },
  },
]
