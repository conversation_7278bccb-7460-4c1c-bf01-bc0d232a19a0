import { useWorkspaces, useDeleteWorkspace } from './hooks'
import { Table, TableBody, TableContainer, Paper, TableCell, TableRow } from '@mui/material'
import TableHeaderRow from '../TableHeaderRow'
import Loader from '../Loader'
import ErrorDisplay from '../ErrorDisplay'
import { columns, ColumnType } from './columns'
import { Workspace } from '../../../api/types'
import { useModal } from '../../context/modal'
import WorkspaceDrawer from '../WorkspaceDrawer'
import ConfirmDeleteModal from './ConfirmDeleteModal'

export default function WorspaceTable() {
  const { data, isLoading, isError } = useWorkspaces()
  const modal = useModal()
  const deleteWorkspace = useDeleteWorkspace()

  const handleEdit = async (workspace: Workspace) => {
    await modal.appoint(<WorkspaceDrawer workspaceId={workspace[ColumnType.Id]} />)
  }

  const handleDelete = async (workspace: Workspace) => {
    const selectedId: string = await modal.appoint(<ConfirmDeleteModal id={workspace.id} />)

    if (selectedId != null) {
      await deleteWorkspace.mutate({
        id: selectedId,
      })
    }
  }

  if (isLoading) return <Loader />

  if (isError || data == null) return <ErrorDisplay />

  return (
    <Paper>
      <TableContainer>
        <Table stickyHeader aria-label="Workspace Table">
          <TableHeaderRow columns={columns} />
          <TableBody>
            {data.map((row: Workspace, idx: number) => (
              <TableRow hover key={idx}>
                {columns.map((column, idy) => (
                  <TableCell key={idy}>
                    {column.format
                      ? column.format(row, {
                          onEdit: () => handleEdit(row),
                          onDelete: () => handleDelete(row),
                        })
                      : row[column.id]}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Paper>
  )
}
