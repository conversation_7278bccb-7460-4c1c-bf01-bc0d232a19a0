import { Helmet } from 'react-helmet-async'
import TableHeader from '../TableHeader'
import WorkspaceTable from '../WorkspaceTable'
import { useModal } from '../../context/modal'
import WorkspaceDrawer from '../WorkspaceDrawer'
import Breadcrumbs from '../Breadcrumbs'

export default function EstimatesList() {
  const modal = useModal()

  return (
    <div>
      <Helmet>
        <title>Estimates | Project</title>
      </Helmet>
      <Breadcrumbs />

      <TableHeader
        label="Estimates"
        createLabel="New Estimates"
        onCreate={async () => {
          await modal.appoint(<WorkspaceDrawer />)
        }}
      />

      <WorkspaceTable />
    </div>
  )
}
