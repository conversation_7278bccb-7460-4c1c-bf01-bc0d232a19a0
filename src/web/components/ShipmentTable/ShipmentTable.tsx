import { useParams } from 'react-router-dom'
import { Table, TableBody, TableContainer, Paper, TableCell, TableRow } from '@mui/material'

import { useDeleteShipment, useWorkspace } from './hooks'
import TableHeaderRow from '../TableHeaderRow'
import Loader from '../Loader'
import ErrorDisplay from '../ErrorDisplay'
import { columns } from './columns'
import { Shipment } from '../../../api/types'
import { useModal } from '../../context/modal'
import ShipmentDrawer from '../ShipmentDrawer'
import NoItemsPlaceholder from '../NoItemsPlaceholder'
import ConfirmDeleteModal from './ConfirmDeleteModal'

type BuildShipmentParams = {
  workspaceId: string
  buildId: string
}

export default function BuildShipmentTable() {
  const { workspaceId, buildId } = useParams<BuildShipmentParams>() as BuildShipmentParams
  const { data, isLoading, isError } = useWorkspace({ id: workspaceId })
  const modal = useModal()
  const deleteShipment = useDeleteShipment()

  const handleEdit = async (shipment: Shipment) => {
    await modal.appoint(
      <ShipmentDrawer workspaceId={workspaceId} buildId={buildId} shipment={shipment} />
    )
  }

  const handleDelete = async (shipment: Shipment) => {
    const shipmentId: string = await modal.appoint(<ConfirmDeleteModal id={shipment.id} />)

    if (shipmentId != null) {
      await deleteShipment.mutate({
        workspaceId,
        buildId,
        shipmentId,
      })
    }
  }

  if (isLoading) return <Loader />

  if (isError || data == null) return <ErrorDisplay />

  const currentBuild = data.buildShipments.find((item) => {
    return item.id === buildId
  })

  if (currentBuild == null) return <ErrorDisplay />

  if (currentBuild.shipments.length === 0) {
    return <NoItemsPlaceholder message="There are no shipments to display" />
  }

  return (
    <Paper>
      <TableContainer>
        <Table stickyHeader aria-label="Build Table">
          <TableHeaderRow columns={columns} />
          <TableBody>
            {currentBuild.shipments.map((row: Shipment, idx: number) => (
              <TableRow hover key={idx}>
                {columns.map((column, idy) => (
                  <TableCell key={idy}>
                    {column.format
                      ? column.format(row, {
                          onEdit: () => handleEdit(row),
                          onDelete: () => handleDelete(row),
                        })
                      : row[column.id]}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Paper>
  )
}
