import { Box } from '@mui/material'

import { Shipment } from '../../../api/types'
import { Column } from '../../types/table'
import TableActions from '../TableActions'

export enum ColumnType {
  Id = 'id',
  Cost = 'cost',
  Description = 'description',
  OrderNumber = 'orderNumber',
}

export const columns: Column<ColumnType, Shipment>[] = [
  {
    id: ColumnType.Id,
    label: 'ID',
    width: 360,
  },
  {
    id: ColumnType.Cost,
    label: 'Cost',
    width: 230,
    format: (shipment) => {
      const formatter = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
      })

      return <Box>{formatter.format(shipment[ColumnType.Cost])}</Box>
    },
  },
  {
    id: ColumnType.Description,
    label: 'Description',
  },
  {
    id: ColumnType.OrderNumber,
    label: 'Order Number',
    width: 260,
  },
  {
    id: ColumnType.Id,
    width: 72,
    format: (data, { onEdit, onDelete }) => {
      const navItems: any = [
        {
          label: 'Edit',
          onClick: () => onEdit(),
        },
        {
          label: 'Delete',
          onClick: () => onDelete(),
        },
      ]
      return <TableActions items={navItems} />
    },
  },
]
