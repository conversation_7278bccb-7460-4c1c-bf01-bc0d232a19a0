import { useMutation, useQueryClient, useQuery } from 'react-query'

import Project<PERSON>pi from '../../api'
import { Workspace } from '../../../api/types'

export function useWorkspace({ id }: { id: string }) {
  return useQuery<Workspace, Error>(['get-workspace'], () => ProjectApi.getWorkspace(id))
}

interface DeleteShipmentMutationProps {
  workspaceId: string
  buildId: string
  shipmentId: string
}

export function useDeleteShipment() {
  const queryClient = useQueryClient()
  return useMutation(
    async ({ workspaceId, buildId, shipmentId }: DeleteShipmentMutationProps) => {
      return ProjectApi.deleteShipment(workspaceId, buildId, shipmentId)
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries('get-workspaces')
        queryClient.invalidateQueries('get-workspace')
      },
    }
  )
}
