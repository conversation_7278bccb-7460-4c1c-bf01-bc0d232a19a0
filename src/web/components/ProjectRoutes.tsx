import { Routes, Route } from 'react-router-dom'

import WorkspaceList from './WorkspaceList'
import BuildList from './BuildList'
import ShipmentList from './ShipmentList'
import EstimatesList from './EstimatesList'

import Layout from './Layout'
import About from './About'

function ProjectRoutes() {
  return (
    <>
      <Routes>
        <Route path="/" element={<Layout />}>
          <Route path="/workspaces/:workspaceId" element={<BuildList />} />
          <Route path="/workspaces/:workspaceId/:buildId" element={<ShipmentList />} />

          <Route path="/workspaces" element={<WorkspaceList />} />
          <Route path="/estimates" element={<EstimatesList />} />
          <Route path="/" element={<About />} />
        </Route>
      </Routes>
    </>
  )
}

export default ProjectRoutes
