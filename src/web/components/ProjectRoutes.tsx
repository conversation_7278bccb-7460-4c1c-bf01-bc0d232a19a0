import { Routes, Route } from 'react-router-dom'

import WorkspaceList from './WorkspaceList'
import BuildList from './BuildList'
import ShipmentList from './ShipmentList'
import Estimates from './Estimates'

import Layout from './Layout'
import About from './About'
import NewEstimate from './NewEstimate'

function ProjectRoutes() {
  return (
    <>
      <Routes>
        <Route path="/" element={<Layout />}>
          <Route path="/workspaces/:workspaceId" element={<BuildList />} />
          <Route path="/workspaces/:workspaceId/:buildId" element={<ShipmentList />} />

          <Route path="/workspaces" element={<WorkspaceList />} />
          <Route path="/estimates" element={<Estimates />} />
          <Route path="/estimates/new" element={<NewEstimate />} />

          <Route path="/" element={<About />} />
        </Route>
      </Routes>
    </>
  )
}

export default ProjectRoutes
