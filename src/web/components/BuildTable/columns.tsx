import { Box } from '@mui/material'
import { useNavigate, useParams } from 'react-router-dom'

import { ShipmentTable } from '../../../api/types'
import { Column } from '../../types/table'
import TableActions from '../TableActions'

export enum ColumnType {
  Id = 'id',
  BuildNumber = 'buildNumber',
}

const BuildIdColumn = ({ build }: { build: ShipmentTable }) => {
  const navigate = useNavigate()
  const { workspaceId } = useParams()
  return (
    <Box
      onClick={() => navigate(`/workspaces/${workspaceId}/${build.id}`)}
      style={{ cursor: 'pointer' }}
    >
      {build[ColumnType.Id]}
    </Box>
  )
}

const BuildNumberColumn = ({ build }: { build: ShipmentTable }) => {
  const navigate = useNavigate()
  const { workspaceId } = useParams()
  return (
    <Box
      onClick={() => navigate(`/workspaces/${workspaceId}/${build.id}`)}
      style={{ cursor: 'pointer' }}
    >
      {build[ColumnType.BuildNumber]}
    </Box>
  )
}

export const columns: Column<ColumnType, ShipmentTable>[] = [
  {
    id: ColumnType.Id,
    label: 'ID',
    format: (build) => <BuildIdColumn build={build} />,
    width: 360,
  },
  {
    id: ColumnType.BuildNumber,
    label: 'Build Number',
    format: (build) => <BuildNumberColumn build={build} />,
  },
  {
    id: ColumnType.Id,
    width: 72,
    format: (data, { onEdit, onDelete }) => {
      const navItems: any = [
        {
          label: 'Edit',
          onClick: () => onEdit(),
        },
        {
          label: 'Delete',
          onClick: () => onDelete(),
        },
      ]
      return <TableActions items={navItems} />
    },
  },
]
