import { Typography } from '@mui/material'

import ConfirmDelete from '../ConfirmDelete'

interface Props {
  resultHook?: {
    resolve: (id?: string) => void
    reject: () => void
  }
  id: string
}

export default function ConfirmDeleteModal({ id, resultHook }: Props) {
  return (
    <ConfirmDelete id={id} resultHook={resultHook} label="Confirm Delete Build">
      <>
        <Typography variant="h5">Delete Build</Typography>

        <Typography variant="body1" style={{ marginTop: '20px' }}>
          This build will be deleted immediately and permanently. Once deleted, it can no longer be
          used.
        </Typography>
        <Typography variant="body1" style={{ marginTop: '10px' }}>
          Do you want to delete this build?
        </Typography>
      </>
    </ConfirmDelete>
  )
}
