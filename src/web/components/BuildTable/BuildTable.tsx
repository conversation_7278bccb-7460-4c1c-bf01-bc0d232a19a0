import { useParams } from 'react-router-dom'
import { Table, TableBody, TableContainer, Paper, TableCell, TableRow } from '@mui/material'

import { useWorkspace, useDeleteBuild } from './hooks'
import TableHeaderRow from '../TableHeaderRow'
import Loader from '../Loader'
import ErrorDisplay from '../ErrorDisplay'
import { columns } from './columns'
import { ShipmentTable } from '../../../api/types'
import { useModal } from '../../context/modal'
import BuildDrawer from '../BuildDrawer'
import NoItemsPlaceholder from '../NoItemsPlaceholder'
import ConfirmDeleteModal from './ConfirmDeleteModal'

type BuildShipmentParams = {
  workspaceId: string
}

export default function BuildShipmentTable() {
  const { workspaceId } = useParams<BuildShipmentParams>() as BuildShipmentParams
  const { data, isLoading, isError } = useWorkspace({ id: workspaceId })
  const modal = useModal()
  const deleteBuild = useDeleteBuild()

  const handleEdit = async (build: ShipmentTable) => {
    await modal.appoint(<BuildDrawer workspaceId={workspaceId} build={build} />)
  }

  const handleDelete = async (build: ShipmentTable) => {
    const selectedId: string = await modal.appoint(<ConfirmDeleteModal id={build.id} />)

    if (selectedId != null) {
      await deleteBuild.mutate({
        workspaceId,
        buildId: selectedId,
      })
    }
  }

  if (isLoading) return <Loader />

  if (isError || data == null) return <ErrorDisplay />

  if (data.buildShipments.length === 0) {
    return <NoItemsPlaceholder message="There are no builds to display" />
  }

  return (
    <Paper>
      <TableContainer>
        <Table stickyHeader aria-label="Build Shipment Table">
          <TableHeaderRow columns={columns} />
          <TableBody>
            {data.buildShipments.map((row: ShipmentTable, idx: number) => (
              <TableRow hover key={idx}>
                {columns.map((column, idy) => (
                  <TableCell key={idy}>
                    {column.format
                      ? column.format(row, {
                          onEdit: () => handleEdit(row),
                          onDelete: () => handleDelete(row),
                        })
                      : row[column.id]}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Paper>
  )
}
