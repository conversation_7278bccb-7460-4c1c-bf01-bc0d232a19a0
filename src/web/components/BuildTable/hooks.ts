import { useMutation, useQueryClient, useQuery } from 'react-query'

import ProjectApi from '../../api'
import { Workspace } from '../../../api/types'

export function useWorkspace({ id }: { id: string }) {
  return useQuery<Workspace, Error>(['get-workspace'], () => ProjectApi.getWorkspace(id))
}

interface DeleteBuildMutationProps {
  workspaceId: string
  buildId: string
}

export function useDeleteBuild() {
  const queryClient = useQueryClient()
  return useMutation(
    async ({ workspaceId, buildId }: DeleteBuildMutationProps) => {
      return ProjectApi.deleteBuild(workspaceId, buildId)
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries('get-workspaces')
        queryClient.invalidateQueries('get-workspace')
      },
    }
  )
}
