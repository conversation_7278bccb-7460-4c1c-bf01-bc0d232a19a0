import { Helmet } from 'react-helmet-async'
import TableHeader from '../TableHeader'
import BuildTable from '../BuildTable'
import { useParams } from 'react-router-dom'
import Breadcrumbs from '../Breadcrumbs'
import { useModal } from '../../context/modal'
import BuildDrawer from '../BuildDrawer'

type BuildShipmentParams = {
  workspaceId: string
}

export default function BuildList() {
  const modal = useModal()
  const { workspaceId } = useParams<BuildShipmentParams>() as BuildShipmentParams

  return (
    <div>
      <Helmet>
        <title>Build Shipments | Project</title>
      </Helmet>
      <Breadcrumbs />
      <TableHeader
        label="Build Shipments"
        createLabel="New Build Shipment"
        onCreate={async () => {
          await modal.appoint(<BuildDrawer workspaceId={workspaceId} />)
        }}
      />
      <BuildTable />
    </div>
  )
}
