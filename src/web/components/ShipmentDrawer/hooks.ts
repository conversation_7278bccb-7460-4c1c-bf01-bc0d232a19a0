import { useMutation, useQueryClient, useQuery } from 'react-query'
import ProjectApi from '../../api'
import { ShipmentDTO, Workspace } from '../../../api/types'

interface ShipmentMutationProps {
  workspaceId: string
  buildId: string
  shipmentId?: string
  payload: ShipmentDTO
}

export function useMutateShipment() {
  const queryClient = useQueryClient()
  return useMutation(
    async ({ workspaceId, buildId, shipmentId, payload }: ShipmentMutationProps) => {
      if (shipmentId != null) {
        return ProjectApi.updateShipment(workspaceId, buildId, shipmentId, payload)
      }
      return ProjectApi.createShipment(workspaceId, buildId, payload)
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries('get-workspaces')
        queryClient.invalidateQueries('get-workspace')
      },
    }
  )
}

export function useWorkspace({ id }: { id: string }) {
  return useQuery<Workspace, Error>(['get-workspace'], () => ProjectApi.getWorkspace(id))
}
