import { Formik } from 'formik'
import { TextField, Box } from '@mui/material'
//@ts-ignore: types are not available
import CurrencyTextField from '@lupus-ai/mui-currency-textfield'

import { Shipment, ShipmentDTO } from '../../../api/types'
import { useModal } from '../../context/modal'
import FormActions from '../FormActions'

interface Props {
  shipment?: Shipment
  onSubmit: (payload: ShipmentDTO) => void
}

enum FormField {
  Cost = 'cost',
  Description = 'description',
  OrderNumber = 'orderNumber',
}

interface FormValues {
  [FormField.Cost]: string
  [FormField.Description]: string
  [FormField.OrderNumber]: string
}

export default function ShipmentForm({ shipment, onSubmit }: Props) {
  const modal = useModal()

  const initialValues: FormValues = {
    [FormField.Cost]: shipment != null ? shipment[FormField.Cost].toString() : '',
    [FormField.Description]: shipment != null ? shipment[FormField.Description] : '',
    [FormField.OrderNumber]: shipment != null ? shipment[FormField.OrderNumber] : '',
  }

  return (
    <Formik<FormValues>
      initialValues={initialValues}
      onSubmit={async (values, actions) => {
        const payload: ShipmentDTO = {
          [FormField.OrderNumber]: values[FormField.OrderNumber],
          [FormField.Cost]: parseFloat(values[FormField.Cost]),
          [FormField.Description]: values[FormField.Description],
        }
        await onSubmit(payload)
      }}
    >
      {({ values, errors, isSubmitting, handleSubmit, setFieldValue, isValid }) => {
        return (
          <form
            onSubmit={handleSubmit}
            onKeyDown={(event: any) => {
              if (event.key === 'Escape') {
                modal.dismiss()
              }
            }}
            style={{ width: '100%' }}
          >
            <Box display="flex" marginBottom="20px">
              <TextField
                id={FormField.OrderNumber}
                autoFocus={true}
                label="Order Number"
                variant="outlined"
                size="small"
                fullWidth={true}
                value={values[FormField.OrderNumber]}
                onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                  setFieldValue(FormField.OrderNumber, event.target.value)
                }}
              />
            </Box>

            <Box display="flex" marginBottom="20px">
              <CurrencyTextField
                id="cost-field"
                label="Cost"
                variant="outlined"
                size="small"
                value={values[FormField.Cost]}
                currencySymbol="$"
                outputFormat="string"
                onChange={(event: React.ChangeEvent<HTMLInputElement>, value: string) =>
                  setFieldValue(FormField.Cost, event.target.value)
                }
              />
            </Box>

            <Box display="flex" marginBottom="20px">
              <TextField
                id={FormField.Description}
                label="Description"
                multiline
                rows={4}
                variant="outlined"
                size="small"
                fullWidth={true}
                value={values[FormField.Description]}
                onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                  setFieldValue(FormField.Description, event.target.value)
                }}
              />
            </Box>

            <FormActions
              label={shipment != null ? 'Save' : 'Create'}
              onSubmit={() => handleSubmit()}
            />
          </form>
        )
      }}
    </Formik>
  )
}
