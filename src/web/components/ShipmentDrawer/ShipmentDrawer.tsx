import { Shipment, ShipmentDTO } from '../../../api/types'
import { useModal } from '../../context/modal'
import CustomDrawer from '../CustomDrawer'
import ShipmentForm from './ShipmentForm'
import { useMutateShipment } from './hooks'

interface Props {
  workspaceId: string
  buildId: string
  shipment?: Shipment
  resultHook?: {
    resolve: () => void
    reject: () => void
  }
}

export default function ShipmentDrawer({ workspaceId, buildId, shipment, resultHook }: Props) {
  const { mutate } = useMutateShipment()
  const modal = useModal()

  return (
    <CustomDrawer label={shipment != null ? 'Edit Shipment' : 'Create Shipment'}>
      <ShipmentForm
        shipment={shipment}
        onSubmit={async (payload: ShipmentDTO) => {
          mutate(
            {
              workspaceId,
              buildId,
              shipmentId: shipment?.id,
              payload,
            },
            {
              onSuccess: () => modal.dismiss(),
            }
          )
        }}
      />
    </CustomDrawer>
  )
}
