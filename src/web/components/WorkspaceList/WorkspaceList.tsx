import { Helmet } from 'react-helmet-async'
import TableHeader from '../TableHeader'
import WorkspaceTable from '../WorkspaceTable'
import { useModal } from '../../context/modal'
import WorkspaceDrawer from '../WorkspaceDrawer'
import Breadcrumbs from '../Breadcrumbs'

/** Homepage list of all workspaces that have been created */
export default function WorkspaceList() {
  const modal = useModal()

  return (
    <div className="WorkspaceList">
      <Helmet>
        <title>Workspaces | Project</title>
      </Helmet>
      <Breadcrumbs />

      <TableHeader
        label="Workspaces"
        createLabel="New Workspace"
        onCreate={async () => {
          await modal.appoint(<WorkspaceDrawer />)
        }}
      />

      <WorkspaceTable />
    </div>
  )
}
