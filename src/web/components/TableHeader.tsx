import React from 'react'
import { Box, Button, Typography } from '@mui/material'

import makeStyles from '@mui/styles/makeStyles'

import { faPlus } from '@fortawesome/free-solid-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'

import { ReactNode } from 'react'
import { IconProp } from '@fortawesome/fontawesome-svg-core'

const useStyles: any = makeStyles({
  createButton: {
    marginRight: '15px',
    marginLeft: '4px',
  },
})

interface Props {
  label: string
  createLabel?: string
  onCreate?: () => void
  children?: ReactNode
}

export default function TableHeader({ label, createLabel, onCreate, children }: Props) {
  const classes = useStyles()

  return (
    <Box
      display="flex"
      flexDirection="row"
      alignItems="center"
      flex="0 0 auto"
      padding="12px 30px 11px 30px"
    >
      <Typography variant="h6">{label}</Typography>
      {createLabel != null && onCreate != null && (
        <Box display="flex" flex="1 1 auto" marginLeft="20px">
          <Button
            variant="outlined"
            color="primary"
            classes={{ startIcon: classes.createButton }}
            startIcon={<FontAwesomeIcon icon={faPlus as IconProp} />}
            onClick={() => onCreate()}
          >
            {createLabel}
          </Button>
        </Box>
      )}
      {children}
    </Box>
  )
}
