import { Box, Breadcrumbs as B<PERSON><PERSON>rum<PERSON>Mui, <PERSON>, Typography } from '@mui/material'
import NavigateNextIcon from '@mui/icons-material/NavigateNext'

import { useParams, Link as RouterLink } from 'react-router-dom'
import { useWorkspace } from './hooks'
import { getBuild } from '../../utilities/data'

type RouteParams = {
  workspaceId?: string
  buildId?: string
}

export default function Breadcrumbs() {
  const { workspaceId, buildId } = useParams<RouteParams>() as RouteParams
  const { data } = useWorkspace({ id: workspaceId })

  if (workspaceId != null && data == null) return null

  const currentBuild = data != null ? getBuild(data, buildId) : undefined

  return (
    <Box display="flex" marginY="20px" marginLeft="20px">
      <BreadcrumbsMui separator={<NavigateNextIcon fontSize="small" />} aria-label="breadcrumbs">
        <Link underline="hover" component={RouterLink} to="/">
          Project
        </Link>
        <Link underline="hover" component={RouterLink} to="/workspaces">
          Workspaces
        </Link>
        {buildId != null && data != null && (
          <Link underline="hover" component={RouterLink} to={`/workspaces/${workspaceId}`}>
            {data.title.length > 0 ? data.title : 'Untitled Workspace'}
          </Link>
        )}
        {currentBuild != null && (
          <Box display="flex" alignItems="center">
            <Typography color="textPrimary">
              {currentBuild.buildNumber.length > 0 ? currentBuild.buildNumber : 'Untitled Build'}
            </Typography>
          </Box>
        )}
        {buildId == null && data != null && (
          <Box display="flex" alignItems="center">
            <Typography color="textPrimary">
              {data.title.length > 0 ? data.title : 'Untitled Workspace'}
            </Typography>
          </Box>
        )}
      </BreadcrumbsMui>
    </Box>
  )
}
