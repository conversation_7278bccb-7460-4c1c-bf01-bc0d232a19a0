import { Box, TableCell, TableHead, TableRow, TableSortLabel } from '@mui/material'
import React from 'react'

interface Props {
  columns: any[]
  orderBy?: any
  order?: any
  onChangeSort?: (event: React.MouseEvent<unknown>, property: any) => void
}

export default function TableHeaderRow({ columns, orderBy, order, onChangeSort }: Props) {
  return (
    <TableHead>
      <TableRow>
        {columns.map((column, idx) => (
          <TableCell
            key={idx}
            style={{
              minWidth: column?.width,
              maxWidth: column?.width,
              width: column?.width,
            }}
          >
            {column?.enableSort === true && (
              <TableSortLabel
                active={orderBy === column.id}
                direction={orderBy === column.id ? order : 'asc'}
                onClick={(event) => {
                  if (onChangeSort != null) onChangeSort(event, column.id)
                }}
              >
                {column?.label ?? ''}
              </TableSortLabel>
            )}
            {(column?.enableSort == null || column?.enableSort === false) && (
              <Box>{column?.label ?? ''}</Box>
            )}
          </TableCell>
        ))}
      </TableRow>
    </TableHead>
  )
}
