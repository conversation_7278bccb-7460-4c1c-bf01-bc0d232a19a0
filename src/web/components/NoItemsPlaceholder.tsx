import { Box } from '@mui/material'

import { faInfoCircle } from '@fortawesome/free-solid-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'

import { colors } from '../utilities/styleList'
import { IconProp } from '@fortawesome/fontawesome-svg-core'

interface Props {
  message: string
}

export default function NoItemsPlaceholder({ message }: Props) {
  return (
    <Box
      display="flex"
      width="450px"
      style={{ backgroundColor: colors.gallery }}
      margin="20px"
      padding="20px"
      borderRadius="4px"
    >
      <Box display="flex" marginRight="20px" marginTop="2px">
        <FontAwesomeIcon icon={faInfoCircle as IconProp} />
      </Box>
      {message}
    </Box>
  )
}
