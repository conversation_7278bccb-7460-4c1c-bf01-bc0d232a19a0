import { <PERSON>, Button, Link, Typography } from '@mui/material'
import { useNavigate } from 'react-router-dom'

export default function About() {
  const navigate = useNavigate()
  return (
    <Box display="flex" flexDirection="column" margin="20px">
      <Typography variant="h5">Enterprise resource planning (ERP)</Typography>
      <Box marginTop="20px">
        <Typography variant="body1">
          ERP - building a cloud collaboration platform to help operations teams (Supply Chain /
          Finance / Logistics) manage internal and external processes. Platform can be used to
          visualize and interact with supply chain.
        </Typography>
        <Typography variant="body1">
          ERP provides an integrated and continuously updated view of core business processes using
          common databases maintained by a database management system. ERP systems track business
          resources—cash, raw materials, production capacity—and the status of business commitments:
          orders, purchase orders, and payroll. The applications that make up the system share data
          across various departments (manufacturing, purchasing, sales, accounting, etc.) that
          provide the data. ERP facilitates information flow between all business functions and
          manages connections to outside stakeholders. Source:
          <Link href="https://en.wikipedia.org/wiki/Enterprise_resource_planning">
            https://en.wikipedia.org/wiki/Enterprise_resource_planning
          </Link>
        </Typography>
      </Box>

      <Box marginTop="40px" display="flex">
        <Button
          variant="contained"
          onClick={() => {
            navigate(`/workspaces`)
          }}
        >
          Take Tour
        </Button>
      </Box>
    </Box>
  )
}
