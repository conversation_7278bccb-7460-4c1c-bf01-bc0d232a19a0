import React from 'react'
import { Mo<PERSON>, Box, Button } from '@mui/material'
import makeStyles from '@mui/styles/makeStyles'

import { useModal } from '../context/modal'

const useStyles: any = makeStyles({
  paper: {
    position: 'absolute',
    width: 550,
    backgroundColor: '#FFFFFF',
    padding: 20,
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    outline: 0,
  },
})

interface Props {
  resultHook?: {
    resolve: (id?: string) => void
    reject: () => void
  }
  id: string
  children: React.ReactElement
  label?: string
}

export default function ConfirmDelete({
  id,
  children,
  label = 'confirm delete',
  resultHook,
}: Props) {
  const classes = useStyles()
  const modal = useModal()

  return (
    <Modal
      open={modal.modal != null}
      onClose={() => {
        resultHook && resultHook.resolve()

        modal.dismiss()
      }}
      onKeyDown={async (event: any) => {
        if (event.key === 'Enter') {
          resultHook && resultHook.resolve(id)

          modal.dismiss()
        } else if (event.key === 'Escape') {
          resultHook && resultHook.resolve()

          modal.dismiss()
        }
      }}
      aria-labelledby={label}
    >
      <Box className={classes.paper}>
        {children}

        <Box display="flex" marginTop="30px" justifyContent="flex-end">
          <Button
            style={{ marginRight: '10px' }}
            onClick={() => {
              resultHook && resultHook.resolve()

              modal.dismiss()
            }}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={async () => {
              resultHook && resultHook.resolve(id)

              modal.dismiss()
            }}
          >
            Delete
          </Button>
        </Box>
      </Box>
    </Modal>
  )
}
