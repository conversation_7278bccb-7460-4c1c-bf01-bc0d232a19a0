import React from 'react'
import { Formik, FieldArray } from 'formik'
import {
  Box,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  IconButton,
  Typography,
  FormHelperText,
} from '@mui/material'
import { Add as AddIcon, Delete as DeleteIcon } from '@mui/icons-material'
import { useModal } from '../../context/modal'
import FormActions from '../FormActions'

// Types and enums
enum LineItemType {
  Labor = 'Labor',
  Materials = 'Materials',
  Equipment = 'Equipment',
}

enum LaborItems {
  Digout = 'Digout',
  Paving = 'Paving',
}

enum MaterialItems {
  Asphalt = 'Asphalt',
  Concrete = 'Concrete',
  Gravel = 'Gravel',
  Sealcoating = 'Sealcoating',
}

enum EquipmentItems {
  Bobcat = 'Bobcat',
  Trucks = 'Trucks',
  Paver = 'Paver',
}

interface LineItem {
  type: LineItemType | ''
  item: string
  units: string
  time: string
  rate: string
  margin: string
}

interface FormValues {
  lineItems: LineItem[]
}

interface Props {
  onSubmit: (values: FormValues) => void
  initialValues?: FormValues
}

// Validation functions
const validateLineItem = (lineItem: LineItem) => {
  const errors: Partial<LineItem> = {}

  if (!lineItem.type) {
    errors.type = 'Type is required'
  }

  if (!lineItem.item) {
    errors.item = 'Item is required'
  }

  if (!lineItem.units) {
    errors.units = 'Units is required'
  }

  if ((lineItem.type === LineItemType.Labor || lineItem.type === LineItemType.Equipment) && !lineItem.time) {
    errors.time = 'Time is required for Labor and Equipment'
  }

  if (!lineItem.rate) {
    errors.rate = 'Rate is required'
  } else if (isNaN(parseFloat(lineItem.rate)) || parseFloat(lineItem.rate) < 0) {
    errors.rate = 'Rate must be a valid positive number'
  }

  if (!lineItem.margin) {
    errors.margin = 'Margin is required'
  } else if (isNaN(parseFloat(lineItem.margin)) || parseFloat(lineItem.margin) < 0 || parseFloat(lineItem.margin) >= 100) {
    errors.margin = 'Margin must be a number between 0 and 99'
  }

  return errors
}

const validateForm = (values: FormValues) => {
  const errors: any = {}

  if (!values.lineItems || values.lineItems.length === 0) {
    errors.lineItems = 'At least one line item is required'
  } else {
    const lineItemErrors = values.lineItems.map(validateLineItem)
    if (lineItemErrors.some(error => Object.keys(error).length > 0)) {
      errors.lineItems = lineItemErrors
    }
  }

  return errors
}

// Helper functions
const getItemOptions = (type: LineItemType | '') => {
  switch (type) {
    case LineItemType.Labor:
      return Object.values(LaborItems)
    case LineItemType.Materials:
      return Object.values(MaterialItems)
    case LineItemType.Equipment:
      return Object.values(EquipmentItems)
    default:
      return []
  }
}

const calculateCost = (lineItem: LineItem): number => {
  const units = parseFloat(lineItem.units) || 0
  const time = parseFloat(lineItem.time) || 0
  const rate = parseFloat(lineItem.rate) || 0

  if (lineItem.type === LineItemType.Labor || lineItem.type === LineItemType.Equipment) {
    return units * time * rate
  } else if (lineItem.type === LineItemType.Materials) {
    return units * rate
  }
  return 0
}

const calculatePrice = (lineItem: LineItem): number => {
  const cost = calculateCost(lineItem)
  const margin = parseFloat(lineItem.margin) || 0

  if (margin >= 100) return cost // Prevent division by zero or negative
  return cost / (1 - margin / 100)
}

const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount)
}

export default function EstimateForm({ onSubmit, initialValues }: Props) {
  const modal = useModal()

  const defaultInitialValues: FormValues = {
    lineItems: [
      {
        type: '',
        item: '',
        units: '',
        time: '',
        rate: '',
        margin: '',
      },
    ],
  }

  return (
    <Formik
      initialValues={initialValues || defaultInitialValues}
      validate={validateForm}
      onSubmit={(values, { setSubmitting }) => {
        onSubmit(values)
        setSubmitting(false)
      }}
    >
      {({ values, errors, touched, handleSubmit, setFieldValue, isSubmitting, isValid }) => (
        <form
          onSubmit={handleSubmit}
          onKeyDown={(event: any) => {
            if (event.key === 'Escape') {
              modal.dismiss()
            }
          }}
          style={{ width: '100%' }}
        >
          <Box marginBottom="20px">
            <Typography variant="h6" gutterBottom>
              Estimate Line Items
            </Typography>

            <FieldArray name="lineItems">
              {({ push, remove }) => (
                <Box>
                  <TableContainer component={Paper} variant="outlined">
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell>Type</TableCell>
                          <TableCell>Item</TableCell>
                          <TableCell>Units</TableCell>
                          <TableCell>Time</TableCell>
                          <TableCell>Rate</TableCell>
                          <TableCell>Cost</TableCell>
                          <TableCell>Margin</TableCell>
                          <TableCell>Price</TableCell>
                          <TableCell>Actions</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
