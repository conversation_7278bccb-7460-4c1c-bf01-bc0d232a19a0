import { useState } from 'react'
import { Box, Typography, Paper } from '@mui/material'
import EstimateForm from './EstimateForm'

// Demo component to showcase the EstimateForm
export default function EstimateFormDemo() {
  const [submittedData, setSubmittedData] = useState<any>(null)

  const handleSubmit = (values: any) => {
    console.log('Form submitted with values:', values)
    setSubmittedData(values)

    // Calculate totals for display
    const totalCost = values.lineItems.reduce((sum: number, item: any) => {
      const units = parseFloat(item.units) || 0
      const time = parseFloat(item.time) || 0
      const rate = parseFloat(item.rate) || 0

      if (item.type === 'Labor' || item.type === 'Equipment') {
        return sum + units * time * rate
      } else if (item.type === 'Materials') {
        return sum + units * rate
      }
      return sum
    }, 0)

    const totalPrice = values.lineItems.reduce((sum: number, item: any) => {
      const units = parseFloat(item.units) || 0
      const time = parseFloat(item.time) || 0
      const rate = parseFloat(item.rate) || 0
      const margin = parseFloat(item.margin) || 0

      let cost = 0
      if (item.type === 'Labor' || item.type === 'Equipment') {
        cost = units * time * rate
      } else if (item.type === 'Materials') {
        cost = units * rate
      }

      const price = margin >= 100 ? cost : cost / (1 - margin / 100)
      return sum + price
    }, 0)

    alert(
      `Estimate saved!\nTotal Cost: $${totalCost.toFixed(2)}\nTotal Price: $${totalPrice.toFixed(
        2
      )}`
    )
  }

  // Sample initial data for testing
  const sampleData = {
    lineItems: [
      {
        type: 'Labor',
        item: 'Digout',
        units: '5',
        time: '8',
        rate: '25.00',
        margin: '20',
      },
      {
        type: 'Materials',
        item: 'Asphalt',
        units: '10',
        time: '',
        rate: '150.00',
        margin: '15',
      },
      {
        type: 'Equipment',
        item: 'Bobcat',
        units: '1',
        time: '4',
        rate: '75.00',
        margin: '25',
      },
    ],
  }

  return (
    <Box padding="20px">
      <Typography variant="h4" gutterBottom>
        Estimate Form Demo
      </Typography>

      <Typography variant="body1" paragraph>
        This form demonstrates all the features requested:
      </Typography>

      <Box component="ul" marginBottom="20px">
        <li>Dynamic dropdowns based on type selection</li>
        <li>Conditional time field (only for Labor and Equipment)</li>
        <li>Real-time cost and price calculations</li>
        <li>Comprehensive validation</li>
        <li>Add/remove line items functionality</li>
        <li>Summary totals</li>
      </Box>

      <EstimateForm onSubmit={handleSubmit} />

      {submittedData && (
        <Box marginTop="40px">
          <Paper variant="outlined" sx={{ padding: 2 }}>
            <Typography variant="h6" gutterBottom>
              Last Submitted Data:
            </Typography>
            <pre style={{ fontSize: '12px', overflow: 'auto' }}>
              {JSON.stringify(submittedData, null, 2)}
            </pre>
          </Paper>
        </Box>
      )}
    </Box>
  )
}
