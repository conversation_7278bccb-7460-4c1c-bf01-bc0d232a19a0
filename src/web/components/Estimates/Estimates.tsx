import { Helmet } from 'react-helmet-async'
import TableHeader from '../TableHeader'
import WorkspaceTable from '../WorkspaceTable'
import { useModal } from '../../context/modal'
import NewEstimate from './NewEstimate'
import Breadcrumbs from '../Breadcrumbs'

export default function Estimates() {
  const modal = useModal()

  return (
    <div>
      <Helmet>
        <title>Estimates | Project</title>
      </Helmet>
      <Breadcrumbs />

      <TableHeader
        label="Estimates"
        createLabel="New Estimate"
        onCreate={async () => {
          await modal.appoint(<NewEstimate />)
        }}
      />

      <WorkspaceTable />
    </div>
  )
}
