import React from 'react'
import { Box, Divider, IconButton, ListItemText, Menu, MenuItem } from '@mui/material'
import makeStyles from '@mui/styles/makeStyles'

import MoreVertIcon from '@mui/icons-material/MoreVert'

interface MenuItemType {
  label: string
  isDisabled?: boolean
  isDivider?: boolean
  onClick: (event: React.MouseEvent) => void
}

interface TableActionsProps {
  items: Array<MenuItemType>
}

const useStyles: any = makeStyles({
  button: {
    padding: '2px',
  },
  lockIcon: {
    marginRight: '-25px',
  },
})

export default function TableActions({ items }: TableActionsProps) {
  const [anchorEl, setAnchorEl] = React.useState(null)
  const classes: any = useStyles()

  const open = Boolean(anchorEl)

  const handleClick = (event: any) => {
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  return (
    <Box display="flex">
      <IconButton
        classes={{ root: classes.button }}
        aria-label="more"
        aria-controls="long-menu"
        aria-haspopup="true"
        onClick={handleClick}
      >
        <MoreVertIcon />
      </IconButton>
      <Menu id="long-menu" anchorEl={anchorEl} keepMounted open={open} onClose={handleClose}>
        {items.map((menuItem: MenuItemType, idx: number) => (
          <Box display="flex" flexDirection="column" key={idx}>
            <MenuItem
              disabled={menuItem?.isDisabled}
              onClick={(event) => {
                handleClose()
                menuItem.onClick(event)
              }}
            >
              <Box display="flex" flex="1 1 auto" justifyContent="space-between">
                <ListItemText>{menuItem.label}</ListItemText>
              </Box>
            </MenuItem>
            {menuItem.isDivider === true && (
              <Divider style={{ marginTop: '0px', marginBottom: '0px' }} light />
            )}
          </Box>
        ))}
      </Menu>
    </Box>
  )
}
