import { BuildDTO, ShipmentTable } from '../../../api/types'
import { useModal } from '../../context/modal'
import CustomDrawer from '../CustomDrawer'
import BuildForm from './BuildForm'
import { useMutateBuild } from './hooks'

interface Props {
  workspaceId: string
  build?: ShipmentTable
  resultHook?: {
    resolve: () => void
    reject: () => void
  }
}

export default function BuildDrawer({ workspaceId, build, resultHook }: Props) {
  const { mutate } = useMutateBuild()
  const modal = useModal()

  return (
    <CustomDrawer label={build != null ? 'Edit Build' : 'Create Build'}>
      <BuildForm
        build={build}
        onSubmit={async (payload: BuildDTO) => {
          mutate(
            {
              workspaceId,
              buildId: build?.id,
              payload,
            },
            {
              onSuccess: () => modal.dismiss(),
            }
          )
        }}
      />
    </CustomDrawer>
  )
}
