import { useMutation, useQueryClient, useQuery } from 'react-query'
import ProjectApi from '../../api'
import { BuildDTO, Workspace } from '../../../api/types'

interface BuildMutationProps {
  workspaceId: string
  buildId?: string
  payload: BuildDTO
}

export function useMutateBuild() {
  const queryClient = useQueryClient()
  return useMutation(
    async ({ workspaceId, buildId, payload }: BuildMutationProps) => {
      if (buildId != null) {
        return ProjectApi.updateBuild(workspaceId, buildId, payload)
      }

      return ProjectApi.createBuild(workspaceId, payload)
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries('get-workspaces')
        queryClient.invalidateQueries('get-workspace')
      },
    }
  )
}

export function useWorkspace({ id }: { id: string }) {
  return useQuery<Workspace, Error>(['get-workspace'], () => ProjectApi.getWorkspace(id))
}
