import { Formik } from 'formik'
import { TextField, Box } from '@mui/material'

import { BuildDTO, ShipmentTable } from '../../../api/types'
import { useModal } from '../../context/modal'
import FormActions from '../FormActions'

interface Props {
  build?: ShipmentTable
  onSubmit: (payload: BuildDTO) => void
}

enum FormField {
  BuildNumber = 'buildNumber',
}

interface FormValues {
  [FormField.BuildNumber]: string
}

export default function BuildForm({ build, onSubmit }: Props) {
  const modal = useModal()

  const initialValues: FormValues = {
    [FormField.BuildNumber]: build != null ? build.buildNumber : '',
  }

  return (
    <Formik<FormValues>
      initialValues={initialValues}
      onSubmit={async (values, actions) => {
        const payload: BuildDTO = {
          [FormField.BuildNumber]: values[FormField.BuildNumber],
        }
        await onSubmit(payload)
      }}
    >
      {({ values, errors, isSubmitting, handleSubmit, setFieldValue, isValid }) => {
        return (
          <form
            onSubmit={handleSubmit}
            onKeyDown={(event: any) => {
              if (event.key === 'Escape') {
                modal.dismiss()
              }
            }}
            style={{ width: '100%' }}
          >
            <Box display="flex" marginBottom="20px">
              <TextField
                id={FormField.BuildNumber}
                autoFocus={true}
                label="Build Number"
                variant="outlined"
                size="small"
                fullWidth={true}
                value={values[FormField.BuildNumber]}
                onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                  setFieldValue(FormField.BuildNumber, event.target.value)
                }}
              />
            </Box>

            <FormActions
              label={build != null ? 'Save' : 'Create'}
              onSubmit={() => handleSubmit()}
            />
          </form>
        )
      }}
    </Formik>
  )
}
