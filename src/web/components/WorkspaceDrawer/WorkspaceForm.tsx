import { Formik } from 'formik'
import { TextField, Box } from '@mui/material'

import { Workspace, WorkspaceDTO } from '../../../api/types'
import { useModal } from '../../context/modal'
import FormActions from '../FormActions'

interface Props {
  workspace?: Workspace
  onSubmit: (payload: WorkspaceDTO) => void
}

enum FormField {
  Title = 'title',
}

interface FormValues {
  [FormField.Title]: string
}

export default function WorkspaceForm({ workspace, onSubmit }: Props) {
  const modal = useModal()

  const initialValues: FormValues = {
    [FormField.Title]: workspace != null ? workspace.title : '',
  }

  return (
    <Formik<FormValues>
      initialValues={initialValues}
      onSubmit={async (values, actions) => {
        const payload: WorkspaceDTO = {
          [FormField.Title]: values[FormField.Title],
        }
        await onSubmit(payload)
      }}
    >
      {({ values, errors, isSubmitting, handleSubmit, setFieldValue, isValid }) => {
        return (
          <form
            onSubmit={handleSubmit}
            onKeyDown={(event: any) => {
              if (event.key === 'Escape') {
                modal.dismiss()
              }
            }}
            style={{ width: '100%' }}
          >
            <Box display="flex" marginBottom="20px">
              <TextField
                id={FormField.Title}
                autoFocus
                label="Title"
                variant="outlined"
                size="small"
                fullWidth={true}
                value={values[FormField.Title]}
                onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                  setFieldValue(FormField.Title, event.target.value)
                }}
              />
            </Box>

            <FormActions
              label={workspace != null ? 'Save' : 'Create'}
              onSubmit={() => handleSubmit()}
            />
          </form>
        )
      }}
    </Formik>
  )
}
