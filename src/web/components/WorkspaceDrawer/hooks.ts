import { useMutation, useQueryClient, useQuery } from 'react-query'
import Project<PERSON>pi from '../../api'
import { Workspace, WorkspaceDTO } from '../../../api/types'

interface WorkspaceMutationProps {
  id?: string
  payload: WorkspaceDTO
}

export function useMutateWorkspace() {
  const queryClient = useQueryClient()
  return useMutation(
    async ({ payload, id }: WorkspaceMutationProps) => {
      if (id != null) {
        return ProjectApi.updateWorkspace(id, payload)
      }

      return ProjectApi.createWorkspace(payload)
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries('get-workspaces')
        queryClient.invalidateQueries('get-workspace')
      },
    }
  )
}

export function useWorkspace({ id }: { id?: string }) {
  return useQuery<Workspace, Error>(['get-workspace', { id }], () => ProjectApi.getWorkspace(id), {
    enabled: id != null,
  })
}
