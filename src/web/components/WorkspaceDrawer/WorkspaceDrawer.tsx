import { WorkspaceDTO } from '../../../api/types'
import { useModal } from '../../context/modal'
import CustomDrawer from '../CustomDrawer'
import Loader from '../Loader'
import WorkspaceForm from './WorkspaceForm'
import { useMutateWorkspace, useWorkspace } from './hooks'

interface Props {
  workspaceId?: string
  resultHook?: {
    resolve: () => void
    reject: () => void
  }
}

export default function WorkspaceDrawer({ workspaceId, resultHook }: Props) {
  const { mutate } = useMutateWorkspace()

  const modal = useModal()
  const { data, isLoading } = useWorkspace({ id: workspaceId })

  if (isLoading) {
    return <Loader />
  }

  if (workspaceId != null && data == null) return null

  return (
    <CustomDrawer label={workspaceId != null ? 'Edit Workspace' : 'Create Workspace'}>
      <WorkspaceForm
        workspace={data}
        onSubmit={async (payload: WorkspaceDTO) => {
          mutate(
            { id: workspaceId, payload },
            {
              onSuccess: () => modal.dismiss(),
            }
          )
        }}
      />
    </CustomDrawer>
  )
}
