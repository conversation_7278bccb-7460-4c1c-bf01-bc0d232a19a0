import { ReactElement, ReactNode, createContext, useContext } from 'react';

interface ModalContextValue {
  appoint: <ReturnType>(modal: ReactElement<{}>) => Promise<ReturnType>;
  dismiss: () => void;
  modal?: ReactNode;
}

const defaultMethodImplementation = () => {
  throw new Error('Modal context has not been implemented.');
};

export const defaultValue: ModalContextValue = {
  appoint: defaultMethodImplementation,
  dismiss: defaultMethodImplementation,
};

export const ModalContext = createContext<ModalContextValue>(defaultValue);

export const useModal = () => useContext(ModalContext);
