import React, { ReactNode, useState, ReactElement } from 'react';
import Deferred from '../utilities/Deferred';

import { SnackbarContext } from '../context/snackbar';

interface Props {
  children: ReactNode;
}

const SnackbarManager = ({ children }: Props) => {
  const [snackbar, setSnackbar] = useState<ReactElement<unknown> | undefined>(
    undefined,
  );

  return (
    <SnackbarContext.Provider
      value={{
        appoint: async function <ReturnType>(
          snackbar: ReactElement<{}>,
        ): Promise<ReturnType> {
          const deferred = new Deferred<ReturnType>();

          setSnackbar({
            ...snackbar,
            props: {
              ...snackbar.props,
              // provide modal with promise hooks
              resultHook: deferred,
            },
          });

          return deferred;
        },
        dismiss: () => setSnackbar(undefined),
        snackbar,
      }}
    >
      {snackbar}
      {children}
    </SnackbarContext.Provider>
  );
};

export default SnackbarManager;
