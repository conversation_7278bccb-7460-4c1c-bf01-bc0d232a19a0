import React from 'react'
import { BrowserRouter as Router } from 'react-router-dom'
import { HelmetProvider } from 'react-helmet-async'
import { QueryClient, QueryClientProvider } from 'react-query'
import { createTheme, ThemeProvider } from '@mui/material/styles'

import { colors } from './utilities/styleList'
import ProjectRoutes from './components/ProjectRoutes'
import SnackbarManager from './managers/SnackbarManager'
import ModalManager from './managers/ModalManager'

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
    },
  },
})

const theme = createTheme({
  typography: {
    fontFamily: 'Inter, sans-serif',
  },
  palette: {
    primary: {
      main: colors.ebonyClay,
    },
  },
})

function App() {
  return (
    <ThemeProvider theme={theme}>
      <QueryClientProvider client={queryClient}>
        <HelmetProvider>
          <Router>
            <ModalManager>
              <SnackbarManager>
                <ProjectRoutes />
              </SnackbarManager>
            </ModalManager>
          </Router>
        </HelmetProvider>
      </QueryClientProvider>
    </ThemeProvider>
  )
}

export default App
