import axios from 'axios'
import { BuildDTO, ShipmentDTO, WorkspaceDTO } from '../api/types'

const BASE_URL = 'http://localhost:8080'

/** The API for the app, for querying, creating and updating workspaces */
class ProjectApi {
  /** Returns the ID and title of every existing workspace */
  static async getWorkspaces() {
    try {
      const req = await axios.get(BASE_URL)
      const { workspaces } = req.data
      return workspaces
    } catch (err) {
      throw new Error('Unable to fetch workspaces')
    }
  }
  static async getWorkspace(id?: string) {
    try {
      const req = await axios.get(`${BASE_URL}/${id}`)
      const { workspace } = req.data
      return workspace
    } catch (err) {
      throw new Error(`Unable to fetch ${id} workspace`)
    }
  }
  static async updateWorkspace(id: string, payload: WorkspaceDTO) {
    try {
      const req = await axios.patch(`${BASE_URL}/workspaces/${id}`, payload)
      const { workspaces } = req.data
      return workspaces
    } catch (err) {
      throw new Error('Unable to update workspace')
    }
  }
  static async deleteWorkspace(id: string) {
    try {
      const req = await axios.delete(`${BASE_URL}/workspaces/${id}`)
      const { workspaces } = req.data
      return workspaces
    } catch (err) {
      throw new Error('Unable to delete workspace')
    }
  }
  static async createWorkspace(payload: any) {
    try {
      const req = await axios.post(`${BASE_URL}/workspaces`, payload)
      const { workspace } = req.data
      return workspace
    } catch (err) {
      throw new Error('Unable to create workspace')
    }
  }

  static async createBuild(workspaceId: string, payload: BuildDTO) {
    try {
      const req = await axios.post(`${BASE_URL}/workspaces/${workspaceId}/build`, payload)
      const { workspace } = req.data
      return workspace
    } catch (err) {
      throw new Error('Unable to create build')
    }
  }

  static async updateBuild(workspaceId: string, buildId: string, payload: BuildDTO) {
    try {
      const req = await axios.patch(
        `${BASE_URL}/workspaces/${workspaceId}/build/${buildId}`,
        payload
      )
      const { workspace } = req.data
      return workspace
    } catch (err) {
      throw new Error('Unable to create build')
    }
  }

  static async deleteBuild(workspaceId: string, buildId: string) {
    try {
      const req = await axios.delete(`${BASE_URL}/workspaces/${workspaceId}/build/${buildId}`)
      const { workspaces } = req.data
      return workspaces
    } catch (err) {
      throw new Error('Unable to delete build')
    }
  }

  static async createShipment(workspaceId: string, buildId: string, payload: ShipmentDTO) {
    try {
      const req = await axios.post(
        `${BASE_URL}/workspaces/${workspaceId}/build/${buildId}/shipment`,
        payload
      )
      const { workspace } = req.data
      return workspace
    } catch (err) {
      throw new Error('Unable to create shipment')
    }
  }

  static async updateShipment(
    workspaceId: string,
    buildId: string,
    shipmentId: string,
    payload: ShipmentDTO
  ) {
    try {
      const req = await axios.patch(
        `${BASE_URL}/workspaces/${workspaceId}/build/${buildId}/shipment/${shipmentId}`,
        payload
      )
      const { workspace } = req.data
      return workspace
    } catch (err) {
      throw new Error('Unable to update shipment')
    }
  }

  static async deleteShipment(workspaceId: string, buildId: string, shipmentId: string) {
    try {
      const req = await axios.delete(
        `${BASE_URL}/workspaces/${workspaceId}/build/${buildId}/shipment/${shipmentId}`
      )
      const { workspace } = req.data
      return workspace
    } catch (err) {
      throw new Error('Unable to delete shipment')
    }
  }
}

export default ProjectApi
