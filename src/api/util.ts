import { all, findOne, insert, update, deleteObj } from './db/db'
import { BuildDTO, Workspace, WorkspaceDTO } from './types'
import { v4 as uuidv4 } from 'uuid'

/** Returns a list of all workspaces in the database */
export function getWorkspaces(dbString: string): Workspace[] {
  return all(dbString, 'workspaces')
}

/** Returns a single workspace from the database */
export function getWorkspace(dbString: string, id: string): Workspace {
  return findOne(dbString, 'workspaces', id)
}

export function deleteWorkspace(dbString: string, id: string) {
  return deleteObj(dbString, 'workspaces', id)
}

/** Create a workspace in the database */
export function createWorkspace(dbString: string, payload: WorkspaceDTO): Workspace {
  const workspace: Workspace = {
    id: uuidv4(),
    ...payload,
    buildShipments: [],
  }

  insert(dbString, 'workspaces', workspace)
  return workspace
}

export function createBuild(dbString: string, workspaceId: string, payload: BuildDTO): Workspace {
  createBuild(dbString, workspaceId, payload)
  return findOne(dbString, 'workspaces', workspaceId)
}

/** Update a workspace in the database */
export function updateWorkspace(dbString: string, id: string, payload: WorkspaceDTO): Workspace {
  update(dbString, 'workspaces', id, payload)
  return findOne(dbString, 'workspaces', id)
}
