import express from 'express'
import cors from 'cors'
import {
  createWorkspace,
  getWorkspace,
  getWorkspaces,
  updateWorkspace,
  deleteWorkspace,
} from './util'
import {
  createBuild,
  createShipment,
  deleteBuild,
  deleteShipment,
  reset,
  updateBuild,
  updateShipment,
} from './db/db'
import { BuildDTO, ShipmentDTO, WorkspaceDTO } from './types'

const app = express()
app.use(cors())
app.use(express.json())

const port = 8080
const dbString = '../database.txt'

/** Admin endpoint for resetting the database */
app.get('/reset', (req, res) => {
  reset(dbString)
  res.send('Reset database')
})

/** Returns the workspace with the given ID */
app.get('/:workspaceId', (req, res) => {
  res.json({ workspace: getWorkspace(dbString, req.params.workspaceId) })
})

/** Create Shipment for the build the given workspaceId and buildId. Returns the updated workspace */
app.post('/workspaces/:workspaceId/build/:buildId/shipment', (req, res) => {
  const workspaceId = req.params.workspaceId
  const buildId = req.params.buildId

  const payload: ShipmentDTO = req.body
  res.json({ workspace: createShipment(dbString, workspaceId, buildId, payload) })
})

/** Update Shipment for the build the given workspaceId, buildId, and shipmentId. Returns the updated workspace */
app.patch('/workspaces/:workspaceId/build/:buildId/shipment/:shipmentId', (req, res) => {
  const workspaceId = req.params.workspaceId
  const buildId = req.params.buildId
  const shipmentId = req.params.shipmentId

  const payload: ShipmentDTO = req.body
  res.json({ workspace: updateShipment(dbString, workspaceId, buildId, shipmentId, payload) })
})

/** Delete Shipment for the build the given workspaceId, buildId, and shipmentId. Returns the updated workspace */
app.delete('/workspaces/:workspaceId/build/:buildId/shipment/:shipmentId', (req, res) => {
  const workspaceId = req.params.workspaceId
  const buildId = req.params.buildId
  const shipmentId = req.params.shipmentId

  res.json({ workspace: deleteShipment(dbString, workspaceId, buildId, shipmentId) })
})

/** Create Build  for the workspace the given workspaceId. Returns the updated workspace */
app.post('/workspaces/:workspaceId/build', (req, res) => {
  const workspaceId = req.params.workspaceId
  const payload: BuildDTO = req.body
  res.json({ workspace: createBuild(dbString, workspaceId, payload) })
})

/** Update Build for the workspace the given workspaceId and buildId. Returns the updated workspace */
app.patch('/workspaces/:workspaceId/build/:buildId', (req, res) => {
  const workspaceId = req.params.workspaceId
  const buildId = req.params.buildId
  const payload: BuildDTO = req.body
  res.json({ workspace: updateBuild(dbString, workspaceId, buildId, payload) })
})

/** Delete Build for the workspace the given workspaceId and buildId. Returns the updated workspace */
app.delete('/workspaces/:workspaceId/build/:buildId', (req, res) => {
  const workspaceId = req.params.workspaceId
  const buildId = req.params.buildId
  res.json({ workspace: deleteBuild(dbString, workspaceId, buildId) })
})

/** Updates the workspace with the given ID and returns the updated workspace */
app.patch('/workspaces/:workspaceId', (req, res) => {
  const id = req.params.workspaceId
  const payload: WorkspaceDTO = req.body
  res.json({ workspace: updateWorkspace(dbString, id, payload) })
})

/** Creates a new workspace in the database and returns it */
app.post('/workspaces', (req, res) => {
  const payload: WorkspaceDTO = req.body
  res.json({ workspace: createWorkspace(dbString, payload) })
})

app.delete('/workspaces/:workspaceId', (req, res) => {
  deleteWorkspace(dbString, req.params.workspaceId)
  res.json({})
})

/** Returns all workspaces in the database */
app.get('/', (req, res) => {
  const allWorkspaces = getWorkspaces(dbString)
  const workspaces = allWorkspaces.map((workspace) => ({
    id: workspace.id,
    title: workspace.title,
  }))
  res.json({ workspaces })
})

module.exports = app

app.listen(port, () => {
  console.log(`Project is running on port ${port}.`)
})
