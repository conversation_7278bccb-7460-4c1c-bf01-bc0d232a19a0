import fs from 'fs'
import { v4 as uuidv4 } from 'uuid'
import { BuildDTO, Shipment, ShipmentDTO, ShipmentTable, Workspace, WorkspaceDTO } from '../types'
import path from 'path'

const WORKSPACES_KEY = 'workspaces'

export function getFilePath(dbFile: string): string {
  return path.resolve(__dirname, dbFile)
}

/** Read database file, returned parsed data */
const readDbFile = (dbString: string) => {
  const data = fs.readFileSync(getFilePath(dbString), 'utf8')
  const json = JSON.parse(data)
  return json
}

const writeDbFile = (dbFile: string, json: any) => {
  fs.writeFileSync(getFilePath(dbFile), JSON.stringify(json))
}

/** Insert a new object into the database */
export function insert(dbFile: string, key: string, obj: Workspace) {
  const json = readDbFile(dbFile)
  json[key] ||= []
  json[key].push(obj)
  writeDbFile(dbFile, json)
}

/** Update an existing object in the database */
export function update(dbFile: string, key: 'workspaces', id: string, payload: WorkspaceDTO) {
  const json: { workspaces: Workspace[] } = readDbFile(dbFile)
  const updatingIndex = json[key].findIndex((item) => item.id === id)
  if (updatingIndex === -1) {
    throw new Error('Could not find object with id "' + id + '"')
  }
  const updatedWorkspace: Workspace = {
    ...json.workspaces[updatingIndex],
    ...payload,
  }
  json[key].splice(updatingIndex, 1, updatedWorkspace)
  writeDbFile(dbFile, json)
}

/** Create build for existing workspace */
export function createBuild(dbFile: string, workspaceId: string, payload: BuildDTO) {
  const json: { workspaces: Workspace[] } = readDbFile(dbFile)

  const updatingIndex = json[WORKSPACES_KEY].findIndex((item) => item.id === workspaceId)
  if (updatingIndex === -1) {
    throw new Error('Could not find workspace with id "' + workspaceId + '"')
  }
  const workspace = json.workspaces[updatingIndex]
  const updatedWorkspace: Workspace = {
    ...workspace,
    buildShipments: [
      ...workspace.buildShipments,
      {
        id: uuidv4(),
        shipments: [],
        ...payload,
      },
    ],
  }
  json[WORKSPACES_KEY].splice(updatingIndex, 1, updatedWorkspace)
  writeDbFile(dbFile, json)
}

/** Create shipment for existing build */
export function createShipment(
  dbFile: string,
  workspaceId: string,
  buildId: string,
  payload: ShipmentDTO
) {
  const json: { workspaces: Workspace[] } = readDbFile(dbFile)
  const updatingIndex = json[WORKSPACES_KEY].findIndex((item) => item.id === workspaceId)
  if (updatingIndex === -1) {
    throw new Error('Could not find workspace with id "' + workspaceId + '"')
  }
  const workspace = json.workspaces[updatingIndex]
  const buildIndex = workspace.buildShipments.findIndex((item) => item.id === buildId)
  if (buildIndex === -1) {
    throw new Error(`Could not find build with id ${buildId}`)
  }
  const buildShipments: ShipmentTable[] = [...workspace.buildShipments]
  buildShipments[buildIndex] = {
    ...buildShipments[buildIndex],
    shipments: [
      ...buildShipments[buildIndex].shipments,
      {
        id: uuidv4(),
        ...payload,
      },
    ],
  }
  const updatedWorkspace: Workspace = {
    ...workspace,
    buildShipments,
  }
  json[WORKSPACES_KEY].splice(updatingIndex, 1, updatedWorkspace)
  writeDbFile(dbFile, json)
}

/** Update shipment for existing build */
export function updateShipment(
  dbFile: string,
  workspaceId: string,
  buildId: string,
  shipmentId: string,
  payload: ShipmentDTO
) {
  const json: { workspaces: Workspace[] } = readDbFile(dbFile)
  const updatingIndex = json[WORKSPACES_KEY].findIndex((item) => item.id === workspaceId)
  if (updatingIndex === -1) {
    throw new Error('Could not find workspace with id "' + workspaceId + '"')
  }
  const workspace = json.workspaces[updatingIndex]
  const buildIndex = workspace.buildShipments.findIndex((item) => item.id === buildId)
  if (buildIndex === -1) {
    throw new Error(`Could not find build with id ${buildId}`)
  }
  const shipmentIndex = workspace.buildShipments[buildIndex].shipments.findIndex(
    (item) => item.id === shipmentId
  )
  if (shipmentIndex === -1) {
    throw new Error(`Could not find shipment with id ${shipmentId}`)
  }

  const buildShipments: ShipmentTable[] = [...workspace.buildShipments]
  const shipments: Shipment[] = [...buildShipments[buildIndex].shipments]
  shipments[shipmentIndex] = {
    ...shipments[shipmentIndex],
    ...payload,
  }

  buildShipments[buildIndex] = {
    ...buildShipments[buildIndex],
    shipments,
  }

  const updatedWorkspace: Workspace = {
    ...workspace,
    buildShipments,
  }
  json[WORKSPACES_KEY].splice(updatingIndex, 1, updatedWorkspace)
  writeDbFile(dbFile, json)
}

/** Delete shipment for existing build */
export function deleteShipment(
  dbFile: string,
  workspaceId: string,
  buildId: string,
  shipmentId: string
) {
  const json: { workspaces: Workspace[] } = readDbFile(dbFile)
  const updatingIndex = json[WORKSPACES_KEY].findIndex((item) => item.id === workspaceId)
  if (updatingIndex === -1) {
    throw new Error('Could not find workspace with id "' + workspaceId + '"')
  }
  const workspace = json.workspaces[updatingIndex]
  const buildIndex = workspace.buildShipments.findIndex((item) => item.id === buildId)
  if (buildIndex === -1) {
    throw new Error(`Could not find build with id ${buildId}`)
  }

  const buildShipments: ShipmentTable[] = [...workspace.buildShipments]

  buildShipments[buildIndex] = {
    ...buildShipments[buildIndex],
    shipments: buildShipments[buildIndex].shipments.filter((item) => item.id !== shipmentId),
  }

  const updatedWorkspace: Workspace = {
    ...workspace,
    buildShipments,
  }
  json[WORKSPACES_KEY].splice(updatingIndex, 1, updatedWorkspace)
  writeDbFile(dbFile, json)
}

/** Update build for existing workspace */
export function updateBuild(
  dbFile: string,
  workspaceId: string,
  buildId: string,
  payload: BuildDTO
) {
  const json: { workspaces: Workspace[] } = readDbFile(dbFile)
  const updatingIndex = json[WORKSPACES_KEY].findIndex((item) => item.id === workspaceId)
  if (updatingIndex === -1) {
    throw new Error('Could not find workspace with id "' + workspaceId + '"')
  }
  const workspace = json.workspaces[updatingIndex]
  const buildIndex = workspace.buildShipments.findIndex((item) => item.id === buildId)
  if (buildIndex === -1) {
    throw new Error(`Could not find build with id ${buildId}`)
  }
  const buildShipments: ShipmentTable[] = [...workspace.buildShipments]
  buildShipments[buildIndex] = {
    ...buildShipments[buildIndex],
    ...payload,
  }
  const updatedWorkspace: Workspace = {
    ...workspace,
    buildShipments,
  }
  json[WORKSPACES_KEY].splice(updatingIndex, 1, updatedWorkspace)
  writeDbFile(dbFile, json)
}

/** Delete build for existing workspace */
export function deleteBuild(dbFile: string, workspaceId: string, buildId: string) {
  const json: { workspaces: Workspace[] } = readDbFile(dbFile)
  const updatingIndex = json[WORKSPACES_KEY].findIndex((item) => item.id === workspaceId)
  if (updatingIndex === -1) {
    throw new Error('Could not find workspace with id "' + workspaceId + '"')
  }
  const workspace = json.workspaces[updatingIndex]
  const buildShipments = workspace.buildShipments.filter((item) => item.id !== buildId)
  const updatedWorkspace: Workspace = {
    ...workspace,
    buildShipments,
  }
  json[WORKSPACES_KEY].splice(updatingIndex, 1, updatedWorkspace)
  writeDbFile(dbFile, json)
}

/** Delete an existing object from the database */
export function deleteObj(dbFile: string, key: 'workspaces', id: string) {
  const json: { workspaces: Workspace[] } = readDbFile(dbFile)
  const removingIndex = json[key].findIndex((item) => item.id === id)
  if (removingIndex === -1) {
    throw new Error('Could not find object with id "' + id + '"')
  }
  json[key].splice(removingIndex, 1)
  writeDbFile(dbFile, json)
}

/** Return a single object from the database */
export function findOne(dbFile: string, key: 'workspaces', id: string) {
  const json: { workspaces: Workspace[] } = readDbFile(dbFile)
  const result = json[key].find((item) => item.id === id)
  if (!result) {
    throw new Error('Could not find item with id "' + id + '"')
  }
  return result
}

/** Return all objects from the database */
export function all(dbFile: string, key: string) {
  const json = readDbFile(dbFile)
  return json[key]
}

/** Reset the database to a single workspace and invoice */
export function reset(dbFile: string, uuid?: string) {
  const workspaces: Workspace[] = [
    {
      id: uuid ?? uuidv4(),
      title: "Wiley's Shipping",
      buildShipments: [
        {
          id: uuidv4(),
          buildNumber: 'A82D2-108',
          // Initialize the workspace with a single empty build shipment
          shipments: [
            {
              id: uuidv4(),
              description: '64 units',
              orderNumber: '121-5821131-5985042',
              cost: 107_643,
            },
          ],
        },
      ],
    },
  ]
  writeDbFile(dbFile, { workspaces })
}
