{"version": 3, "file": "static/js/496.30cfb627.chunk.js", "mappings": "2bAAA,IAAIA,EAAEC,EAAEC,EAAEC,EAAEC,EAAEC,GAAG,EAAEC,EAAE,SAASN,GAAGO,iBAAiB,YAAY,SAASN,GAAGA,EAAEO,YAAYH,EAAEJ,EAAEQ,UAAUT,EAAEC,GAAG,IAAG,EAAG,EAAES,EAAE,WAAW,OAAOC,OAAOC,aAAaA,YAAYC,kBAAkBD,YAAYC,iBAAiB,cAAc,EAAE,EAAEC,EAAE,WAAW,IAAId,EAAEU,IAAI,OAAOV,GAAGA,EAAEe,iBAAiB,CAAC,EAAEC,EAAE,SAAShB,EAAEC,GAAG,IAAIC,EAAEQ,IAAIP,EAAE,WAAW,OAAOE,GAAG,EAAEF,EAAE,qBAAqBD,IAAIC,EAAEc,SAASC,cAAcJ,IAAI,EAAE,YAAYG,SAASE,aAAa,UAAUjB,EAAEkB,KAAKC,QAAQ,KAAK,MAAM,CAACC,KAAKtB,EAAEuB,WAAM,IAAStB,GAAG,EAAEA,EAAEuB,OAAO,OAAOC,MAAM,EAAEC,QAAQ,GAAGC,GAAG,MAAMC,OAAOC,KAAKC,MAAM,KAAKF,OAAOG,KAAKC,MAAM,cAAcD,KAAKE,UAAU,MAAMC,eAAe/B,EAAE,EAAEgC,EAAE,SAASnC,EAAEC,EAAEC,GAAG,IAAI,GAAGkC,oBAAoBC,oBAAoBC,SAAStC,GAAG,CAAC,IAAIG,EAAE,IAAIiC,qBAAqB,SAASpC,GAAGuC,QAAQC,UAAUC,MAAM,WAAWxC,EAAED,EAAE0C,aAAa,GAAG,IAAI,OAAOvC,EAAEwC,QAAQC,OAAOC,OAAO,CAACzB,KAAKpB,EAAE8C,UAAS,GAAI5C,GAAG,CAAC,IAAIC,CAAC,CAAW,CAAT,MAAMH,GAAG,CAAC,EAAE+C,EAAE,SAAS/C,EAAEC,GAAG,IAAIC,EAAE,SAASA,EAAEC,GAAG,aAAaA,EAAEiB,MAAM,WAAWH,SAAS+B,kBAAkBhD,EAAEG,GAAGF,IAAIgD,oBAAoB,mBAAmB/C,GAAE,GAAI+C,oBAAoB,WAAW/C,GAAE,IAAK,EAAEK,iBAAiB,mBAAmBL,GAAE,GAAIK,iBAAiB,WAAWL,GAAE,EAAG,EAAEgD,EAAE,SAASlD,EAAEC,EAAEC,EAAEC,GAAG,IAAIC,EAAEC,EAAE,OAAO,SAASC,GAAGL,EAAEsB,OAAO,IAAIjB,GAAGH,MAAME,EAAEJ,EAAEsB,OAAOnB,GAAG,UAAK,IAASA,KAAKA,EAAEH,EAAEsB,MAAMtB,EAAEwB,MAAMpB,EAAEJ,EAAEuB,OAAO,SAASxB,EAAEC,GAAG,OAAOD,EAAEC,EAAE,GAAG,OAAOD,EAAEC,EAAE,GAAG,oBAAoB,MAAM,CAApE,CAAsEA,EAAEsB,MAAMrB,GAAGF,EAAEC,GAAG,CAAC,EAAEkD,EAAE,SAASnD,GAAGoD,uBAAuB,WAAW,OAAOA,uBAAuB,WAAW,OAAOpD,GAAG,GAAG,GAAG,EAAEqD,EAAE,SAASrD,GAAGiB,SAASC,aAAaX,iBAAiB,sBAAsB,WAAW,OAAOP,GAAG,IAAG,GAAIA,GAAG,EAAEsD,GAAG,EAAEC,EAAE,WAAW,MAAM,WAAWtC,SAAS+B,iBAAiB/B,SAASC,aAAa,IAAI,CAAC,EAAEsC,EAAE,SAASxD,GAAG,WAAWiB,SAAS+B,iBAAiBM,GAAG,IAAIA,EAAE,qBAAqBtD,EAAEoB,KAAKpB,EAAES,UAAU,EAAEgD,IAAI,EAAEC,EAAE,WAAWnD,iBAAiB,mBAAmBiD,GAAE,GAAIjD,iBAAiB,qBAAqBiD,GAAE,EAAG,EAAEC,EAAE,WAAWR,oBAAoB,mBAAmBO,GAAE,GAAIP,oBAAoB,qBAAqBO,GAAE,EAAG,EAAEG,EAAE,WAAW,OAAOL,EAAE,IAAIA,EAAEC,IAAIG,IAAIpD,GAAG,WAAWsD,YAAY,WAAWN,EAAEC,IAAIG,GAAG,GAAG,EAAE,KAAK,CAAKG,sBAAkB,OAAOP,CAAC,EAAE,EAAEQ,EAAE,SAAS9D,EAAEC,GAAGA,EAAEA,GAAG,CAAC,EAAEoD,GAAG,WAAW,IAAInD,EAAEC,EAAE,CAAC,KAAK,KAAKC,EAAEuD,IAAItD,EAAEW,EAAE,OAAON,EAAEyB,EAAE,SAAS,SAASnC,GAAGA,EAAE+D,SAAS,SAAS/D,GAAG,2BAA2BA,EAAEsB,OAAOZ,EAAEsD,aAAahE,EAAEiE,UAAU7D,EAAEyD,kBAAkBxD,EAAEkB,MAAMQ,KAAKmC,IAAIlE,EAAEiE,UAAUnD,IAAI,GAAGT,EAAEqB,QAAQyC,KAAKnE,GAAGE,GAAE,IAAK,GAAG,IAAIQ,IAAIR,EAAEgD,EAAElD,EAAEK,EAAEF,EAAEF,EAAEmE,kBAAkB9D,GAAG,SAASF,GAAGC,EAAEW,EAAE,OAAOd,EAAEgD,EAAElD,EAAEK,EAAEF,EAAEF,EAAEmE,kBAAkBjB,GAAG,WAAW9C,EAAEkB,MAAMX,YAAYkB,MAAM1B,EAAEK,UAAUP,GAAE,EAAG,GAAG,IAAI,GAAG,EAAEmE,EAAE,SAASrE,EAAEC,GAAGA,EAAEA,GAAG,CAAC,EAAEoD,GAAG,WAAW,IAAInD,EAAEC,EAAE,CAAC,GAAG,KAAKC,EAAEY,EAAE,OAAOX,GAAG,EAAEK,EAAE,EAAEI,EAAE,GAAGuC,EAAE,SAASpD,GAAGI,GAAG,GAAGL,EAAEC,EAAE,EAAEqD,EAAE,SAAStD,GAAGA,EAAE+D,SAAS,SAAS/D,GAAG,IAAIA,EAAEsE,eAAe,CAAC,IAAIrE,EAAEa,EAAE,GAAGX,EAAEW,EAAEA,EAAEyD,OAAO,GAAG7D,GAAGV,EAAEiE,UAAU9D,EAAE8D,UAAU,KAAKjE,EAAEiE,UAAUhE,EAAEgE,UAAU,KAAKvD,GAAGV,EAAEuB,MAAMT,EAAEqD,KAAKnE,KAAKU,EAAEV,EAAEuB,MAAMT,EAAE,CAACd,IAAIU,EAAEN,EAAEmB,QAAQnB,EAAEmB,MAAMb,EAAEN,EAAEsB,QAAQZ,EAAEZ,IAAI,CAAC,GAAG,EAAEqD,EAAEpB,EAAE,eAAemB,GAAGC,IAAIrD,EAAEgD,EAAEG,EAAEjD,EAAED,EAAEF,EAAEmE,kBAAkBN,GAAG,SAAS9D,GAAGK,EAAEL,EAAEuB,MAAMnB,EAAEmB,MAAM,IAAInB,EAAEmB,MAAM,EAAErB,IAAI,IAAI6C,GAAG,WAAWO,EAAEC,EAAEiB,eAAetE,GAAE,EAAG,IAAII,GAAG,WAAWI,EAAE,EAAEL,GAAG,EAAED,EAAEY,EAAE,MAAM,GAAGd,EAAEgD,EAAEG,EAAEjD,EAAED,EAAEF,EAAEmE,kBAAkBjB,GAAG,WAAW,OAAOjD,GAAG,GAAG,IAAI,GAAG,EAAEuE,EAAE,CAACC,SAAQ,EAAGC,SAAQ,GAAIC,EAAE,IAAI/C,KAAKgD,EAAE,SAAS1E,EAAEC,GAAGJ,IAAIA,EAAEI,EAAEH,EAAEE,EAAED,EAAE,IAAI2B,KAAKiD,EAAE7B,qBAAqB8B,IAAI,EAAEA,EAAE,WAAW,GAAG9E,GAAG,GAAGA,EAAEC,EAAE0E,EAAE,CAAC,IAAIxE,EAAE,CAAC4E,UAAU,cAAc1D,KAAKtB,EAAEoB,KAAK6D,OAAOjF,EAAEiF,OAAOC,WAAWlF,EAAEkF,WAAWjB,UAAUjE,EAAES,UAAU0E,gBAAgBnF,EAAES,UAAUR,GAAGE,EAAE4D,SAAS,SAAS/D,GAAGA,EAAEI,EAAE,IAAID,EAAE,EAAE,CAAC,EAAEiF,EAAE,SAASpF,GAAG,GAAGA,EAAEkF,WAAW,CAAC,IAAIjF,GAAGD,EAAES,UAAU,KAAK,IAAIoB,KAAKjB,YAAYkB,OAAO9B,EAAES,UAAU,eAAeT,EAAEoB,KAAK,SAASpB,EAAEC,GAAG,IAAIC,EAAE,WAAW2E,EAAE7E,EAAEC,GAAGG,GAAG,EAAED,EAAE,WAAWC,GAAG,EAAEA,EAAE,WAAW6C,oBAAoB,YAAY/C,EAAEuE,GAAGxB,oBAAoB,gBAAgB9C,EAAEsE,EAAE,EAAElE,iBAAiB,YAAYL,EAAEuE,GAAGlE,iBAAiB,gBAAgBJ,EAAEsE,EAAE,CAAhO,CAAkOxE,EAAED,GAAG6E,EAAE5E,EAAED,EAAE,CAAC,EAAE8E,EAAE,SAAS9E,GAAG,CAAC,YAAY,UAAU,aAAa,eAAe+D,SAAS,SAAS9D,GAAG,OAAOD,EAAEC,EAAEmF,EAAEX,EAAE,GAAG,EAAEY,EAAE,SAASnF,EAAEE,GAAGA,EAAEA,GAAG,CAAC,EAAEiD,GAAG,WAAW,IAAIhD,EAAEK,EAAE,CAAC,IAAI,KAAKI,EAAE6C,IAAIR,EAAEnC,EAAE,OAAOqC,EAAE,SAASrD,GAAGA,EAAEiE,UAAUnD,EAAE+C,kBAAkBV,EAAE5B,MAAMvB,EAAEmF,gBAAgBnF,EAAEiE,UAAUd,EAAEzB,QAAQyC,KAAKnE,GAAGK,GAAE,GAAI,EAAEiD,EAAE,SAAStD,GAAGA,EAAE+D,QAAQV,EAAE,EAAEE,EAAEpB,EAAE,cAAcmB,GAAGjD,EAAE6C,EAAEhD,EAAEiD,EAAEzC,EAAEN,EAAEgE,kBAAkBb,GAAGR,GAAG,WAAWO,EAAEC,EAAEiB,eAAejB,EAAES,YAAY,IAAG,GAAIT,GAAGjD,GAAG,WAAW,IAAIA,EAAE6C,EAAEnC,EAAE,OAAOX,EAAE6C,EAAEhD,EAAEiD,EAAEzC,EAAEN,EAAEgE,kBAAkBjE,EAAE,GAAGF,GAAG,EAAED,EAAE,KAAK8E,EAAEvE,kBAAkBD,EAAE+C,EAAElD,EAAEgE,KAAK7D,GAAGyE,GAAG,GAAG,GAAG,EAAEO,EAAE,EAAEC,EAAE,IAAIC,EAAE,EAAEC,EAAE,SAASzF,GAAGA,EAAE+D,SAAS,SAAS/D,GAAGA,EAAE0F,gBAAgBH,EAAExD,KAAK4D,IAAIJ,EAAEvF,EAAE0F,eAAeF,EAAEzD,KAAKmC,IAAIsB,EAAExF,EAAE0F,eAAeJ,EAAEE,GAAGA,EAAED,GAAG,EAAE,EAAE,EAAE,GAAG,EAAEK,EAAE,WAAW,OAAOxF,EAAEkF,EAAE1E,YAAYiF,kBAAkB,CAAC,EAAEC,EAAE,WAAW,qBAAqBlF,aAAaR,IAAIA,EAAE+B,EAAE,QAAQsD,EAAE,CAACrE,KAAK,QAAQ0B,UAAS,EAAGiD,kBAAkB,IAAI,EAAEC,EAAE,EAAEC,EAAE,WAAW,OAAOL,IAAII,CAAC,EAAEE,EAAE,GAAGC,EAAE,CAAC,EAAEC,EAAE,SAASpG,GAAG,IAAIC,EAAEiG,EAAEA,EAAE3B,OAAO,GAAGrE,EAAEiG,EAAEnG,EAAE0F,eAAe,GAAGxF,GAAGgG,EAAE3B,OAAO,IAAIvE,EAAEqG,SAASpG,EAAEqG,QAAQ,CAAC,GAAGpG,EAAEA,EAAEwB,QAAQyC,KAAKnE,GAAGE,EAAEoG,QAAQvE,KAAKmC,IAAIhE,EAAEoG,QAAQtG,EAAEqG,cAAc,CAAC,IAAIlG,EAAE,CAACwB,GAAG3B,EAAE0F,cAAcY,QAAQtG,EAAEqG,SAAS3E,QAAQ,CAAC1B,IAAImG,EAAEhG,EAAEwB,IAAIxB,EAAE+F,EAAE/B,KAAKhE,EAAE,CAAC+F,EAAEK,MAAM,SAASvG,EAAEC,GAAG,OAAOA,EAAEqG,QAAQtG,EAAEsG,OAAO,IAAIJ,EAAEM,OAAO,IAAIzC,SAAS,SAAS/D,UAAUmG,EAAEnG,EAAE2B,GAAG,GAAG,CAAC,EAAE8E,EAAE,SAASzG,EAAEC,GAAGA,EAAEA,GAAG,CAAC,EAAEoD,GAAG,WAAW,IAAInD,EAAE,CAAC,IAAI,KAAK4F,IAAI,IAAI3F,EAAEC,EAAEY,EAAE,OAAOX,EAAE,SAASL,GAAGA,EAAE+D,SAAS,SAAS/D,GAAIA,EAAE0F,eAAeU,EAAEpG,GAAG,gBAAgBA,EAAEgF,YAAckB,EAAEQ,MAAM,SAASzG,GAAG,OAAOA,EAAEyB,QAAQgF,MAAM,SAASzG,GAAG,OAAOD,EAAEqG,WAAWpG,EAAEoG,UAAUrG,EAAEiE,YAAYhE,EAAEgE,SAAS,GAAG,KAAKmC,EAAEpG,EAAG,IAAI,IAAIC,EAAEC,GAAGD,EAAE8B,KAAK4D,IAAIO,EAAE3B,OAAO,EAAExC,KAAKC,MAAMiE,IAAI,KAAKC,EAAEjG,IAAIC,GAAGA,EAAEoG,UAAUlG,EAAEmB,QAAQnB,EAAEmB,MAAMrB,EAAEoG,QAAQlG,EAAEsB,QAAQxB,EAAEwB,QAAQvB,IAAI,EAAEO,EAAEyB,EAAE,QAAQ9B,EAAE,CAAC0F,kBAAkB9F,EAAE8F,mBAAmB,KAAK5F,EAAE+C,EAAElD,EAAEI,EAAEF,EAAED,EAAEmE,kBAAkB1D,IAAIA,EAAEiC,QAAQ,CAACvB,KAAK,cAAc0B,UAAS,IAAKC,GAAG,WAAW1C,EAAEK,EAAE8D,eAAepE,EAAEmB,MAAM,GAAG0E,IAAI,IAAI7F,EAAEmB,MAAM,EAAEnB,EAAEsB,QAAQ,IAAIvB,GAAE,EAAG,IAAIG,GAAG,WAAW4F,EAAE,GAAGF,EAAEJ,IAAIxF,EAAEY,EAAE,OAAOb,EAAE+C,EAAElD,EAAEI,EAAEF,EAAED,EAAEmE,iBAAiB,IAAI,GAAG,EAAEuC,EAAE,CAAC,EAAEC,EAAE,SAAS5G,EAAEC,GAAGA,EAAEA,GAAG,CAAC,EAAEoD,GAAG,WAAW,IAAInD,EAAEC,EAAE,CAAC,KAAK,KAAKC,EAAEuD,IAAItD,EAAEW,EAAE,OAAON,EAAE,SAASV,GAAG,IAAIC,EAAED,EAAEA,EAAEuE,OAAO,GAAG,GAAGtE,EAAE,CAAC,IAAIE,EAAE4B,KAAKmC,IAAIjE,EAAEgE,UAAUnD,IAAI,GAAGX,EAAEC,EAAEyD,kBAAkBxD,EAAEkB,MAAMpB,EAAEE,EAAEqB,QAAQ,CAACzB,GAAGC,IAAI,CAAC,EAAEmD,EAAElB,EAAE,2BAA2BzB,GAAG,GAAG2C,EAAE,CAACnD,EAAEgD,EAAElD,EAAEK,EAAEF,EAAEF,EAAEmE,kBAAkB,IAAId,EAAE,WAAWqD,EAAEtG,EAAEsB,MAAMjB,EAAE2C,EAAEmB,eAAenB,EAAEW,aAAa2C,EAAEtG,EAAEsB,KAAI,EAAGzB,GAAE,GAAI,EAAE,CAAC,UAAU,SAAS6D,SAAS,SAAS/D,GAAGO,iBAAiBP,EAAEsD,EAAE,CAACuD,MAAK,EAAGlC,SAAQ,GAAI,IAAI5B,EAAEO,GAAE,GAAIhD,GAAG,SAASF,GAAGC,EAAEW,EAAE,OAAOd,EAAEgD,EAAElD,EAAEK,EAAEF,EAAEF,EAAEmE,kBAAkBjB,GAAG,WAAW9C,EAAEkB,MAAMX,YAAYkB,MAAM1B,EAAEK,UAAUkG,EAAEtG,EAAEsB,KAAI,EAAGzB,GAAE,EAAG,GAAG,GAAG,CAAC,GAAG,EAAE4G,EAAE,SAAS9G,EAAEC,GAAGgB,SAASC,aAAamC,GAAG,WAAW,OAAOrD,EAAEC,EAAE,IAAI,aAAagB,SAAS8F,WAAWxG,iBAAiB,QAAQ,WAAW,OAAOP,EAAEC,EAAE,IAAG,GAAI2D,WAAW3D,EAAE,EAAE,EAAE+G,EAAE,SAAShH,EAAEC,GAAGA,EAAEA,GAAG,CAAC,EAAE,IAAIC,EAAE,CAAC,IAAI,MAAMC,EAAEa,EAAE,QAAQZ,EAAE8C,EAAElD,EAAEG,EAAED,EAAED,EAAEmE,kBAAkB0C,GAAG,WAAW,IAAIzG,EAAEK,IAAI,GAAGL,EAAE,CAAC,IAAI8B,EAAE9B,EAAE4G,cAAc,GAAG9E,GAAG,GAAGA,EAAEvB,YAAYkB,MAAM,OAAO3B,EAAEoB,MAAMQ,KAAKmC,IAAI/B,EAAErB,IAAI,GAAGX,EAAEuB,QAAQ,CAACrB,GAAGD,GAAE,GAAIE,GAAG,WAAWH,EAAEa,EAAE,OAAO,IAAIZ,EAAE8C,EAAElD,EAAEG,EAAED,EAAED,EAAEmE,oBAAmB,EAAG,GAAG,CAAC,GAAG,C", "sources": ["../node_modules/web-vitals/dist/web-vitals.js"], "sourcesContent": ["var e,n,t,i,r,a=-1,o=function(e){addEventListener(\"pageshow\",(function(n){n.persisted&&(a=n.timeStamp,e(n))}),!0)},c=function(){return window.performance&&performance.getEntriesByType&&performance.getEntriesByType(\"navigation\")[0]},u=function(){var e=c();return e&&e.activationStart||0},f=function(e,n){var t=c(),i=\"navigate\";return a>=0?i=\"back-forward-cache\":t&&(i=document.prerendering||u()>0?\"prerender\":document.wasDiscarded?\"restore\":t.type.replace(/_/g,\"-\")),{name:e,value:void 0===n?-1:n,rating:\"good\",delta:0,entries:[],id:\"v3-\".concat(Date.now(),\"-\").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:i}},s=function(e,n,t){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var i=new PerformanceObserver((function(e){Promise.resolve().then((function(){n(e.getEntries())}))}));return i.observe(Object.assign({type:e,buffered:!0},t||{})),i}}catch(e){}},d=function(e,n){var t=function t(i){\"pagehide\"!==i.type&&\"hidden\"!==document.visibilityState||(e(i),n&&(removeEventListener(\"visibilitychange\",t,!0),removeEventListener(\"pagehide\",t,!0)))};addEventListener(\"visibilitychange\",t,!0),addEventListener(\"pagehide\",t,!0)},v=function(e,n,t,i){var r,a;return function(o){n.value>=0&&(o||i)&&((a=n.value-(r||0))||void 0===r)&&(r=n.value,n.delta=a,n.rating=function(e,n){return e>n[1]?\"poor\":e>n[0]?\"needs-improvement\":\"good\"}(n.value,t),e(n))}},l=function(e){requestAnimationFrame((function(){return requestAnimationFrame((function(){return e()}))}))},p=function(e){document.prerendering?addEventListener(\"prerenderingchange\",(function(){return e()}),!0):e()},m=-1,h=function(){return\"hidden\"!==document.visibilityState||document.prerendering?1/0:0},g=function(e){\"hidden\"===document.visibilityState&&m>-1&&(m=\"visibilitychange\"===e.type?e.timeStamp:0,T())},y=function(){addEventListener(\"visibilitychange\",g,!0),addEventListener(\"prerenderingchange\",g,!0)},T=function(){removeEventListener(\"visibilitychange\",g,!0),removeEventListener(\"prerenderingchange\",g,!0)},E=function(){return m<0&&(m=h(),y(),o((function(){setTimeout((function(){m=h(),y()}),0)}))),{get firstHiddenTime(){return m}}},L=function(e,n){n=n||{},p((function(){var t,i=[1800,3e3],r=E(),a=f(\"FCP\"),c=s(\"paint\",(function(e){e.forEach((function(e){\"first-contentful-paint\"===e.name&&(c.disconnect(),e.startTime<r.firstHiddenTime&&(a.value=Math.max(e.startTime-u(),0),a.entries.push(e),t(!0)))}))}));c&&(t=v(e,a,i,n.reportAllChanges),o((function(r){a=f(\"FCP\"),t=v(e,a,i,n.reportAllChanges),l((function(){a.value=performance.now()-r.timeStamp,t(!0)}))})))}))},C=function(e,n){n=n||{},p((function(){var t,i=[.1,.25],r=f(\"CLS\"),a=-1,c=0,u=[],p=function(n){a>-1&&e(n)},m=function(e){e.forEach((function(e){if(!e.hadRecentInput){var n=u[0],i=u[u.length-1];c&&e.startTime-i.startTime<1e3&&e.startTime-n.startTime<5e3?(c+=e.value,u.push(e)):(c=e.value,u=[e]),c>r.value&&(r.value=c,r.entries=u,t())}}))},h=s(\"layout-shift\",m);h&&(t=v(p,r,i,n.reportAllChanges),L((function(e){a=e.value,r.value<0&&(r.value=0,t())})),d((function(){m(h.takeRecords()),t(!0)})),o((function(){c=0,a=-1,r=f(\"CLS\",0),t=v(p,r,i,n.reportAllChanges),l((function(){return t()}))})))}))},b={passive:!0,capture:!0},w=new Date,S=function(i,r){e||(e=r,n=i,t=new Date,P(removeEventListener),A())},A=function(){if(n>=0&&n<t-w){var r={entryType:\"first-input\",name:e.type,target:e.target,cancelable:e.cancelable,startTime:e.timeStamp,processingStart:e.timeStamp+n};i.forEach((function(e){e(r)})),i=[]}},I=function(e){if(e.cancelable){var n=(e.timeStamp>1e12?new Date:performance.now())-e.timeStamp;\"pointerdown\"==e.type?function(e,n){var t=function(){S(e,n),r()},i=function(){r()},r=function(){removeEventListener(\"pointerup\",t,b),removeEventListener(\"pointercancel\",i,b)};addEventListener(\"pointerup\",t,b),addEventListener(\"pointercancel\",i,b)}(n,e):S(n,e)}},P=function(e){[\"mousedown\",\"keydown\",\"touchstart\",\"pointerdown\"].forEach((function(n){return e(n,I,b)}))},F=function(t,r){r=r||{},p((function(){var a,c=[100,300],u=E(),l=f(\"FID\"),p=function(e){e.startTime<u.firstHiddenTime&&(l.value=e.processingStart-e.startTime,l.entries.push(e),a(!0))},m=function(e){e.forEach(p)},h=s(\"first-input\",m);a=v(t,l,c,r.reportAllChanges),h&&d((function(){m(h.takeRecords()),h.disconnect()}),!0),h&&o((function(){var o;l=f(\"FID\"),a=v(t,l,c,r.reportAllChanges),i=[],n=-1,e=null,P(addEventListener),o=p,i.push(o),A()}))}))},M=0,k=1/0,D=0,x=function(e){e.forEach((function(e){e.interactionId&&(k=Math.min(k,e.interactionId),D=Math.max(D,e.interactionId),M=D?(D-k)/7+1:0)}))},B=function(){return r?M:performance.interactionCount||0},R=function(){\"interactionCount\"in performance||r||(r=s(\"event\",x,{type:\"event\",buffered:!0,durationThreshold:0}))},H=0,N=function(){return B()-H},O=[],q={},j=function(e){var n=O[O.length-1],t=q[e.interactionId];if(t||O.length<10||e.duration>n.latency){if(t)t.entries.push(e),t.latency=Math.max(t.latency,e.duration);else{var i={id:e.interactionId,latency:e.duration,entries:[e]};q[i.id]=i,O.push(i)}O.sort((function(e,n){return n.latency-e.latency})),O.splice(10).forEach((function(e){delete q[e.id]}))}},_=function(e,n){n=n||{},p((function(){var t=[200,500];R();var i,r=f(\"INP\"),a=function(e){e.forEach((function(e){(e.interactionId&&j(e),\"first-input\"===e.entryType)&&(!O.some((function(n){return n.entries.some((function(n){return e.duration===n.duration&&e.startTime===n.startTime}))}))&&j(e))}));var n,t=(n=Math.min(O.length-1,Math.floor(N()/50)),O[n]);t&&t.latency!==r.value&&(r.value=t.latency,r.entries=t.entries,i())},c=s(\"event\",a,{durationThreshold:n.durationThreshold||40});i=v(e,r,t,n.reportAllChanges),c&&(c.observe({type:\"first-input\",buffered:!0}),d((function(){a(c.takeRecords()),r.value<0&&N()>0&&(r.value=0,r.entries=[]),i(!0)})),o((function(){O=[],H=B(),r=f(\"INP\"),i=v(e,r,t,n.reportAllChanges)})))}))},z={},G=function(e,n){n=n||{},p((function(){var t,i=[2500,4e3],r=E(),a=f(\"LCP\"),c=function(e){var n=e[e.length-1];if(n){var i=Math.max(n.startTime-u(),0);i<r.firstHiddenTime&&(a.value=i,a.entries=[n],t())}},p=s(\"largest-contentful-paint\",c);if(p){t=v(e,a,i,n.reportAllChanges);var m=function(){z[a.id]||(c(p.takeRecords()),p.disconnect(),z[a.id]=!0,t(!0))};[\"keydown\",\"click\"].forEach((function(e){addEventListener(e,m,{once:!0,capture:!0})})),d(m,!0),o((function(r){a=f(\"LCP\"),t=v(e,a,i,n.reportAllChanges),l((function(){a.value=performance.now()-r.timeStamp,z[a.id]=!0,t(!0)}))}))}}))},J=function e(n){document.prerendering?p((function(){return e(n)})):\"complete\"!==document.readyState?addEventListener(\"load\",(function(){return e(n)}),!0):setTimeout(n,0)},K=function(e,n){n=n||{};var t=[800,1800],i=f(\"TTFB\"),r=v(e,i,t,n.reportAllChanges);J((function(){var a=c();if(a){var s=a.responseStart;if(s<=0||s>performance.now())return;i.value=Math.max(s-u(),0),i.entries=[a],r(!0),o((function(){i=f(\"TTFB\",0),(r=v(e,i,t,n.reportAllChanges))(!0)}))}}))};export{C as getCLS,L as getFCP,F as getFID,_ as getINP,G as getLCP,K as getTTFB,C as onCLS,L as onFCP,F as onFID,_ as onINP,G as onLCP,K as onTTFB};\n"], "names": ["e", "n", "t", "i", "r", "a", "o", "addEventListener", "persisted", "timeStamp", "c", "window", "performance", "getEntriesByType", "u", "activationStart", "f", "document", "prerendering", "wasDiscarded", "type", "replace", "name", "value", "rating", "delta", "entries", "id", "concat", "Date", "now", "Math", "floor", "random", "navigationType", "s", "PerformanceObserver", "supportedEntryTypes", "includes", "Promise", "resolve", "then", "getEntries", "observe", "Object", "assign", "buffered", "d", "visibilityState", "removeEventListener", "v", "l", "requestAnimationFrame", "p", "m", "h", "g", "T", "y", "E", "setTimeout", "firstHiddenTime", "L", "for<PERSON>ach", "disconnect", "startTime", "max", "push", "reportAllChanges", "C", "hadRecentInput", "length", "takeRecords", "b", "passive", "capture", "w", "S", "P", "A", "entryType", "target", "cancelable", "processingStart", "I", "F", "M", "k", "D", "x", "interactionId", "min", "B", "interactionCount", "R", "durationThreshold", "H", "N", "O", "q", "j", "duration", "latency", "sort", "splice", "_", "some", "z", "G", "once", "J", "readyState", "K", "responseStart"], "sourceRoot": ""}