create form using formik. Make sure to include validation. Follow best practices!

Form consists of line items. Each line item has the following fields:

Capture fields:

1. name: type, type: dropdown, valid values: Labor, Materials, Equipment

2. name: item, type: dropdown. This field depends on type. If type is Labor, then the valid values are: <PERSON><PERSON><PERSON>, Paving. If type is Materials, then the valid values are: <PERSON><PERSON><PERSON>, <PERSON>crete, Gravel, Sealcoating. If type is Equipment, then the valid values are: Bobcat, Trucks, Paver.

3. name: units. Type depends on type (field 1). If type is Labor, then the type is text(number). For example, valid values are: 10 crew, 3 crew.
   If type is materials, then type is text(number). For example, valid values are: 10 tons, 10 gallons.
   If type is equipment, then type is text(number). For example, 3, 1, 2.

4. name: time, type: text(number), valid values: any number. Only show this field if type is Labor or Equipment.

5. name: rate, type: text(number), valid values: dollar amount. Example: 10.00, 10.50, 10.55.

6. Display cost. This is a derived field. cost = units _ Time _ rate (if type is Labor or Equipment) or cost = units \* rate (if type is Materials)

7. name: margin. type: text(number), valid values: any number. For example, 10%, 20%, 30%.

8. Display price. This is a derived field. price = cost / (1 - margin/100)

User has ability to add and remove line items.

Each line item is a row in a table.
