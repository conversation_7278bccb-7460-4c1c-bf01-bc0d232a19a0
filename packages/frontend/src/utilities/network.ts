export enum HttpMethods {
  Post = 'POST',
  Get = 'GET',
  Patch = 'PATCH',
  Put = 'PUT',
  Delete = 'DELETE',
}

export const defaultHeaders = {
  'Accept': 'application/json',
  'Content-Type': 'application/json',
};

export const fetchWithHeaders = async (
  input: RequestInfo | URL,
  init: RequestInit = {
    headers: defaultHeaders,
  },
) => {
  const res = await fetch(input, {
    headers: defaultHeaders,
    ...init,
  });
  const response = await res.json();

  if (res.status !== 200 && res.status !== 204 && res.status !== 201) {
    throw response;
  }

  return response;
};
