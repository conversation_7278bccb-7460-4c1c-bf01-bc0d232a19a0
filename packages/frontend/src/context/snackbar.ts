import { ReactElement, ReactNode, createContext, useContext } from 'react';

interface SnackbarContextValue {
  appoint: <ReturnType>(snackbar: ReactElement<{}>) => Promise<ReturnType>;
  dismiss: () => void;
  snackbar?: ReactNode;
}

const defaultMethodImplementation = () => {
  throw new Error('Snackbar context has not been implemented.');
};

export const defaultValue: SnackbarContextValue = {
  appoint: defaultMethodImplementation,
  dismiss: defaultMethodImplementation,
};

export const SnackbarContext =
  createContext<SnackbarContextValue>(defaultValue);

export const useSnackbar = () => useContext(SnackbarContext);
