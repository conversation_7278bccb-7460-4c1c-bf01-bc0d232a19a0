export interface Grouping {
  id: number;
  groupingType: string;
  groupingKey: string;
  severity: string;
  groupedFindingCreated: string;
  sla: string;
  description: string;
  securityAnalyst: string;
  owner: string;
  workflow: string;
  statusStr: string;
  progress: number;
}

export interface PaginatedGrouping {
  count: number;
  rows: Grouping[];
}

export interface GroupingSeverityCounts {
  severity: string;
  count: number;
}

export enum Severity {
  Critical = 'critical',
  High = 'high',
  Low = 'low',
  Medium = 'medium',
}
