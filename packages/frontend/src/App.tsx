import { BrowserRouter } from 'react-router-dom';
import Router from './routes/Router';
import { QueryClient, QueryClientProvider } from 'react-query';
import { HelmetProvider } from 'react-helmet-async';

import SnackbarManager from './managers/SnackbarManager';
import { colors } from './utilities/styleList';
import { createTheme, ThemeProvider } from '@mui/material/styles';
import ModalManager from './managers/ModalManager';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
    },
  },
});

const theme = createTheme({
  palette: {
    primary: {
      main: colors.ebonyClay,
    },
  },
});

function App() {
  return (
    <ThemeProvider theme={theme}>
      <HelmetProvider>
        <QueryClientProvider client={queryClient}>
          <SnackbarManager>
            <ModalManager>
              <BrowserRouter>
                <Router />
              </BrowserRouter>
            </ModalManager>
          </SnackbarManager>
        </QueryClientProvider>
      </HelmetProvider>
    </ThemeProvider>
  );
}

export default App;
