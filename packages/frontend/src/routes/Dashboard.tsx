import { Box, Button } from '@mui/material';
import { Add as AddIcon } from '@mui/icons-material';
import { Helmet } from 'react-helmet-async';
import DashboardTable from '../components/DashboardTable';
import DashboardChart from '../components/DashboardChart';
import { useModal } from '../context/modal';
import FindingDrawer from '../components/FindingDrawer';

export default function Dashboard() {
  const modal = useModal();
  return (
    <Box display="flex" flexDirection="column" flex="1 1 auto" height="100%">
      <Helmet>
        <title>Dashboard | Project</title>
      </Helmet>
      <Box display="flex" width="450px" height="300px">
        <DashboardChart />

        <Box
          display="flex"
          height="37px"
          flex="0 0 200px"
          marginTop="40px"
          marginLeft="20px"
        >
          <Button
            color="primary"
            variant="outlined"
            onClick={async () => await modal.appoint(<FindingDrawer />)}
            startIcon={<AddIcon />}
          >
            New Finding
          </Button>
        </Box>
      </Box>
      <Box display="flex" flexDirection="column" flex="1 1 auto">
        <DashboardTable />
      </Box>
    </Box>
  );
}
