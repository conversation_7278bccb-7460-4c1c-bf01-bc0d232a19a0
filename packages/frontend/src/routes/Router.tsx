import React from 'react';
import { Route, Routes } from 'react-router-dom';
import Dashboard from './Dashboard';
import Estimates from '../components/Estimates';
import Layout from '../components/Layout';

export default function Router() {
  return (
    <Routes>
      <Route path="/" element={<Layout />}>
        <Route index element={<Dashboard />} />
        <Route path="/estimates" element={<Estimates />} />
      </Route>
    </Routes>
  );
}
