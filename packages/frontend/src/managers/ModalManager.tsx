import { ReactNode, useState, ReactElement } from 'react';
import Deferred from '../utilities/Deferred';

import { ModalContext } from '../context/modal';

interface Props {
  children: ReactNode;
}

const ModalManager = ({ children }: Props) => {
  const [modal, setModal] = useState<ReactElement<unknown> | undefined>(
    undefined,
  );
  const [secondary, setSecondary] = useState<ReactElement<unknown> | undefined>(
    undefined,
  );

  return (
    <ModalContext.Provider
      value={{
        appointSecondary: async function <ReturnType>(
          modal: ReactElement<{}>,
        ): Promise<ReturnType> {
          const deferred = new Deferred<ReturnType>();

          setSecondary({
            ...modal,
            props: {
              ...modal.props,
              // provide modal with promise hooks
              resultHook: deferred,
            },
          });

          return deferred;
        },
        dismissSecondary: () => setSecondary(undefined),
        appoint: async function <ReturnType>(
          modal: ReactElement<{}>,
        ): Promise<ReturnType> {
          const deferred = new Deferred<ReturnType>();

          setModal({
            ...modal,
            props: {
              ...modal.props,
              // provide modal with promise hooks
              resultHook: deferred,
            },
          });

          return deferred;
        },
        dismiss: () => setModal(undefined),
        modal,
      }}
    >
      {modal}
      {children}
    </ModalContext.Provider>
  );
};

export default ModalManager;
