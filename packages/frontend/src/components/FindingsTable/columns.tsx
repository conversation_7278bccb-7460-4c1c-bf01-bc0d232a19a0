import { Box, Link } from '@mui/material';

import { Finding } from '../../interfaces';
import { formatDate } from '../../utilities/format';
import { statusLabel, Status } from '../../types/findings';
import CopyButton from '../CopyButton';
import { Column } from '../../types/table';

enum ColumnType {
  Id = 'id',
  SourceSecurityToolName = 'sourceSecurityToolName',
  SourceSecurityToolId = 'sourceSecurityToolId',
  SourceCollaborationToolName = 'sourceCollaborationToolName',
  SourceCollaborationToolId = 'sourceCollaborationToolId',
  Severity = 'severity',
  CreatedAt = 'createdAt',
  TicketCreated = 'ticketCreated',
  Description = 'description',
  Asset = 'asset',
  Status = 'statusStr',
  RemediationUrl = 'remediationUrl',
  RemediationText = 'remediationText',
  GroupedFindingId = 'groupedFindingId',
}

export const columns: Column<ColumnType, Finding>[] = [
  {
    id: ColumnType.Id,
    label: 'ID',
  },
  {
    id: ColumnType.SourceCollaborationToolName,
    label: 'Tool Name',
  },
  {
    id: ColumnType.SourceSecurityToolId,
    label: 'Tool ID',
    maxWidth: 100,
    minWidth: 60,
    format: (item) => {
      return (
        <CopyButton
          title="Copy Tool ID"
          label="Tool ID copied"
          copyValue={item[ColumnType.SourceSecurityToolId]}
          subLabel={`Tool ID: ${item[ColumnType.SourceSecurityToolId]}`}
        />
      );
    },
  },
  {
    id: ColumnType.Severity,
    label: 'Severity',
  },

  {
    id: ColumnType.Description,
    label: 'Description',
    maxWidth: 200,
    minWidth: 200,
  },
  {
    id: ColumnType.Asset,
    label: 'Asset',
    format: (item) => {
      return (
        <CopyButton
          title="Copy Asset"
          label="Asset copied"
          copyValue={item[ColumnType.Asset]}
          subLabel={`Asset: ${item[ColumnType.Asset]}`}
        />
      );
    },
  },
  {
    id: ColumnType.Status,
    label: 'Status',
    minWidth: 80,
    maxWidth: 80,
    format: (item) => {
      const statusKey: Status = item[ColumnType.Status] as Status;
      return statusLabel[statusKey];
    },
  },
  {
    id: ColumnType.RemediationText,
    label: 'Remediation Text',
    maxWidth: 200,
    format: (item) => {
      return (
        <Box>
          {item[ColumnType.RemediationText]}
          {item[ColumnType.RemediationUrl] != null && (
            <Link href={item[ColumnType.RemediationUrl]} target="_blank">
              more
            </Link>
          )}
        </Box>
      );
    },
  },
  {
    id: ColumnType.CreatedAt,
    label: 'Created',
    minWidth: 160,
    format: (item) => formatDate(item[ColumnType.CreatedAt]),
  },
  {
    id: ColumnType.TicketCreated,
    label: 'Ticket Created',
    minWidth: 160,
    format: (item) => formatDate(item[ColumnType.TicketCreated]),
  },
];
