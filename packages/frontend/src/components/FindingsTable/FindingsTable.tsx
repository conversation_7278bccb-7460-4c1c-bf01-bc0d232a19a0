import { Table, TableBody, TableCell, TableRow } from '@mui/material';

import { useFindingsByGroup } from './hooks';
import Loader from '../Loader';
import ErrorDisplay from '../ErrorDisplay';
import TableHeaderRow from '../TableHeaderRow';
import { columns } from './columns';

interface Props {
  id: number;
}

export default function FindingsTable({ id }: Props) {
  const { data, isLoading, isError } = useFindingsByGroup(id);

  if (isLoading) return <Loader />;

  if (isError || data == null) return <ErrorDisplay />;

  return (
    <Table size="small" aria-label="Findings Table">
      <TableHeaderRow columns={columns} />
      <TableBody>
        {data.map((finding, idx) => (
          <TableRow hover key={idx}>
            {columns.map((column, idy) => (
              <TableCell
                key={idy}
                style={{
                  minWidth: column?.minWidth,
                  maxWidth: column?.maxWidth,
                  width: column?.width,
                }}
              >
                {column.format ? column.format(finding) : finding[column.id]}
              </TableCell>
            ))}
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
