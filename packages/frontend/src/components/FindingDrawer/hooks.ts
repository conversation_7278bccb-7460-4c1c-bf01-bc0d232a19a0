import { useMutation, useQueryClient } from 'react-query';
import useProjectClient from '../../hooks/useProjectClient';

interface CreateFindingMutationProps {
  payload: any;
}

export function useCreateFinding() {
  const client = useProjectClient();
  const queryClient = useQueryClient();
  return useMutation(
    async ({ payload }: CreateFindingMutationProps) => {
      return client.createFinding(payload);
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries('get-groupings-paginate');
      },
    },
  );
}
