import { Formik } from 'formik';
import { useModal } from '../../context/modal';
import {
  Box,
  ListItemText,
  MenuItem,
  SelectChangeEvent,
  TextField,
} from '@mui/material';
import FormActions from '../FormActions';
import { Severity } from '../../interfaces/grouping.interface';
import CustomSelect from '../CustomSelect';

/*
SELECT id, grouping_type, grouping_key, severity, grouped_finding_created, sla, description, security_analyst, owner, workflow, status, progress
FROM grouped_findings;
*/

enum FormField {
  Severity = 'severity',
  Description = 'description',
}

interface FormValues {
  [FormField.Severity]: string;
  [FormField.Description]: string;
}

const initialValues: FormValues = {
  [FormField.Severity]: '',
  [FormField.Description]: '',
};

export default function FindingForm() {
  const modal = useModal();
  return (
    <Formik<FormValues>
      initialValues={initialValues}
      onSubmit={async (values) => {}}
    >
      {({ values, errors, isSubmitting, handleSubmit, setFieldValue }) => {
        return (
          <form
            onSubmit={handleSubmit}
            onKeyDown={(event: any) => {
              if (event.key === 'Escape') {
                modal.dismiss();
              }
            }}
          >
            <Box display="flex" marginBottom="30px">
              <CustomSelect
                id={FormField.Severity}
                value={values[FormField.Severity]}
                label="Severity"
                helperText="Severity of a security finding"
                onChange={(event: SelectChangeEvent<unknown>) => {
                  setFieldValue(FormField.Severity, event.target.value);
                }}
              >
                {Object.values(Severity).map((severity, idx) => (
                  <MenuItem key={idx} value={severity}>
                    <ListItemText primary={severity} />
                  </MenuItem>
                ))}
              </CustomSelect>
            </Box>
            <Box display="flex" marginBottom="30px">
              <TextField
                id={FormField.Description}
                autoComplete="chrome-off"
                label="Description"
                variant="outlined"
                size="small"
                fullWidth
                multiline
                rows={4}
                helperText="Optional"
                value={values[FormField.Description]}
                onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                  setFieldValue(FormField.Description, event.target.value);
                }}
              />
            </Box>

            <FormActions label="Create" onSubmit={() => handleSubmit()} />
          </form>
        );
      }}
    </Formik>
  );
}
