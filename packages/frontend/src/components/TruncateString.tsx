import React from 'react';
import { Box, Link } from '@mui/material';

interface Props {
  string: string;
  maximumLength: number;
  canToggle?: boolean;
}

export default function TruncateString({
  string,
  maximumLength,
  canToggle = true,
}: Props) {
  const [isCollapsed, setIsCollapsed] = React.useState(true);

  let renderString = string;
  if (string.length > maximumLength) {
    renderString = `${string.substring(0, maximumLength - 3)}...`;
  }

  return (
    <Box display="inline">
      <Box>{isCollapsed ? renderString : string}</Box>

      {string.length > maximumLength && canToggle && (
        <Link href="#" onClick={() => setIsCollapsed(!isCollapsed)}>
          {isCollapsed ? 'more' : 'less'}
        </Link>
      )}
    </Box>
  );
}
