import { Box, Button, Typography, Grid, Card, CardContent } from '@mui/material';
import CustomModal from './CustomModal';
import { useModal } from '../context/modal';

interface Props {
  resultHook?: {
    resolve: (data?: any) => void;
    reject: () => void;
  };
}

export default function FullscreenModalExample({ resultHook }: Props) {
  const modal = useModal();

  const handleClose = () => {
    if (resultHook) {
      resultHook.reject();
    }
    modal.dismiss();
  };

  const handleSave = () => {
    const data = { saved: true, timestamp: new Date() };
    if (resultHook) {
      resultHook.resolve(data);
    }
    modal.dismiss();
  };

  return (
    <CustomModal label="Fullscreen Dashboard" fullscreen>
      <Box display="flex" flexDirection="column" height="100%">
        {/* Main Content Area */}
        <Box flex="1 1 auto" sx={{ overflowY: 'auto' }}>
          <Typography variant="h4" gutterBottom>
            Fullscreen Modal Example
          </Typography>
          
          <Typography variant="body1" paragraph>
            This modal takes up the entire screen, perfect for complex interfaces,
            dashboards, or detailed forms that need maximum space.
          </Typography>

          {/* Example Grid Layout */}
          <Grid container spacing={3}>
            {[1, 2, 3, 4, 5, 6].map((item) => (
              <Grid item xs={12} sm={6} md={4} key={item}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Card {item}
                    </Typography>
                    <Typography variant="body2">
                      This is example content for card {item}. In a real application,
                      this could be charts, forms, data tables, or any other content
                      that benefits from fullscreen display.
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>

          {/* More content to demonstrate scrolling */}
          <Box mt={4}>
            <Typography variant="h5" gutterBottom>
              Additional Content
            </Typography>
            {[1, 2, 3, 4, 5].map((section) => (
              <Box key={section} mb={3}>
                <Typography variant="h6" gutterBottom>
                  Section {section}
                </Typography>
                <Typography variant="body1" paragraph>
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod 
                  tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, 
                  quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
                </Typography>
              </Box>
            ))}
          </Box>
        </Box>

        {/* Action Bar */}
        <Box 
          display="flex" 
          gap={2} 
          justifyContent="flex-end" 
          pt={2}
          borderTop="1px solid #e0e0e0"
          sx={{ flexShrink: 0 }}
        >
          <Button variant="outlined" onClick={handleClose}>
            Cancel
          </Button>
          <Button variant="contained" onClick={handleSave}>
            Save Changes
          </Button>
        </Box>
      </Box>
    </CustomModal>
  );
}
