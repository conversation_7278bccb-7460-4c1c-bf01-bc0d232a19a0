import { Box, Button, TextField, Typography } from '@mui/material';
import { useState } from 'react';
import CustomModal from './CustomModal';
import { useModal } from '../context/modal';

interface Props {
  resultHook?: {
    resolve: (data?: any) => void;
    reject: () => void;
  };
}

export default function ExampleModal({ resultHook }: Props) {
  const modal = useModal();
  const [formData, setFormData] = useState({
    title: '',
    description: '',
  });

  const handleSubmit = () => {
    console.log('Form submitted:', formData);
    
    // If there's a result hook, resolve with the data
    if (resultHook) {
      resultHook.resolve(formData);
    }
    
    // Close the modal
    modal.dismiss();
  };

  const handleCancel = () => {
    if (resultHook) {
      resultHook.reject();
    }
    modal.dismiss();
  };

  return (
    <CustomModal label="Example Modal" width="500px">
      <Box display="flex" flexDirection="column" gap={3}>
        <Typography variant="body1">
          This is an example of how to use the CustomModal component.
        </Typography>

        <TextField
          label="Title"
          variant="outlined"
          size="small"
          fullWidth
          value={formData.title}
          onChange={(e) => setFormData({ ...formData, title: e.target.value })}
        />

        <TextField
          label="Description"
          variant="outlined"
          size="small"
          fullWidth
          multiline
          rows={3}
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
        />

        <Box display="flex" gap={2} justifyContent="flex-end">
          <Button variant="outlined" onClick={handleCancel}>
            Cancel
          </Button>
          <Button 
            variant="contained" 
            onClick={handleSubmit}
            disabled={!formData.title.trim()}
          >
            Submit
          </Button>
        </Box>
      </Box>
    </CustomModal>
  );
}
