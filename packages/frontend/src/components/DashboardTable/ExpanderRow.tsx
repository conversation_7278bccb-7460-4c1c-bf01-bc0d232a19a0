import { TableRow, Table<PERSON>ell, <PERSON>lapse, Box, Link } from '@mui/material';
import { Grouping } from '../../interfaces';
import { ColumnType } from './columns';
import FindingsTable from '../FindingsTable';

interface Props {
  grouping: Grouping;
  open: boolean;
}

export default function ExpanderRow({ grouping, open }: Props) {
  return (
    <TableRow>
      <TableCell style={{ paddingBottom: 0, paddingTop: 0 }} colSpan={11}>
        <Collapse in={open} timeout="auto" unmountOnExit>
          <Box display="flex" flexDirection="column" margin="10px">
            <Box>Description: {grouping[ColumnType.Description]}</Box>
            <Box>
              <Link href={grouping[ColumnType.GroupingKey]}>
                {grouping[ColumnType.GroupingKey]}
              </Link>
            </Box>
            <FindingsTable id={grouping[ColumnType.Id]} />
          </Box>
        </Collapse>
      </TableCell>
    </TableRow>
  );
}
