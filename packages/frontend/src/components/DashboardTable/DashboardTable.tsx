import React from 'react';
import { useGroupingsPaginate } from './hooks';
import {
  Table,
  TableBody,
  TableContainer,
  Paper,
  TablePagination,
} from '@mui/material';
import TableHeaderRow from '../TableHeaderRow';
import Loader from '../Loader';
import ErrorDisplay from '../ErrorDisplay';
import { columns, ColumnType } from './columns';
import DashboardTableRow from './DashboardTableRow';

const DEFAULT_ROWS_PER_PAGE = 10;
const rowsPerPageOptions = [5, 10, 25];

type Order = 'asc' | 'desc';

export default function DashboardTable() {
  const [page, setPage] = React.useState(0);
  const [rowsPerPage, setRowsPerPage] = React.useState(DEFAULT_ROWS_PER_PAGE);
  const [order, setOrder] = React.useState<Order>('asc');
  const [orderBy, setOrderBy] = React.useState<ColumnType>(ColumnType.Id);

  const { data, isLoading, isError } = useGroupingsPaginate({
    page,
    limit: rowsPerPage,
    order,
    orderBy,
  });

  const handleRequestSort = (
    event: React.MouseEvent<unknown>,
    property: ColumnType,
  ) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
    setPage(0);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleRowsChangePerPage = (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    setRowsPerPage(parseInt(event.target.value, DEFAULT_ROWS_PER_PAGE));
    setPage(0);
  };

  if (isLoading) return <Loader />;

  if (isError || data == null) return <ErrorDisplay />;

  return (
    <Paper>
      <TableContainer>
        <Table stickyHeader aria-label="Dashboard Table">
          <TableHeaderRow
            columns={columns}
            order={order}
            orderBy={orderBy}
            onChangeSort={handleRequestSort}
          />
          <TableBody>
            {data.rows.map((grouping, idx) => (
              <DashboardTableRow grouping={grouping} key={idx} />
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      <TablePagination
        rowsPerPageOptions={rowsPerPageOptions}
        component="div"
        count={data.count}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleRowsChangePerPage}
      />
    </Paper>
  );
}
