import { useQuery } from 'react-query';
import useProjectClient from '../../hooks/useProjectClient';
import { Grouping, PaginatedGrouping, Finding } from '../../interfaces';

export function useGroupings() {
  const client = useProjectClient();

  return useQuery<Grouping[], Error>(['get-groupings'], () =>
    client.getGroupings(),
  );
}

interface GroupingsPaginateQueryProps {
  page: number;
  limit?: number;
  order: 'desc' | 'asc';
  orderBy: string;
}

export function useGroupingsPaginate({
  page,
  limit,
  order,
  orderBy,
}: GroupingsPaginateQueryProps) {
  const client = useProjectClient();

  return useQuery<PaginatedGrouping, Error>(
    ['get-groupings-paginate', { page, limit, order, orderBy }],
    () => client.getGroupingsPaginate({ page, limit, order, orderBy }),
  );
}

export function useFindingsByGroup(groupedFindingId: number) {
  const client = useProjectClient();

  return useQuery<Finding[], Error>(
    ['get-findings-by-group', { groupedFindingId }],
    () => client.getFindingsByGroup(groupedFindingId),
  );
}
