import React from 'react';
import { Box, IconButton } from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import { Grouping } from '../../../interfaces';

import { ColumnType } from '../columns';
import EditableSeverityField from './EditableSeverityField';

interface Props {
  data: Grouping;
}

export default function SeverityColumn({ data }: Props) {
  const [isEdit, setIsEdit] = React.useState(false);

  return (
    <Box display="flex">
      {!isEdit ? (
        <Box>
          {data[ColumnType.Severity]}
          <IconButton
            aria-label="Edit Severity"
            size="small"
            onClick={() => {
              if (!isEdit) setIsEdit(!isEdit);
            }}
          >
            <EditIcon />
          </IconButton>
        </Box>
      ) : (
        <EditableSeverityField data={data} onClose={() => setIsEdit(false)} />
      )}
    </Box>
  );
}
