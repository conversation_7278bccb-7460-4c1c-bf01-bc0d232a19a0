import { useMutation, useQueryClient } from 'react-query';
import useProjectClient from '../../../hooks/useProjectClient';

interface CreateGroupingMutationProps {
  id: string | number;
  payload: any;
}

export function useUpdateGrouping() {
  const client = useProjectClient();
  const queryClient = useQueryClient();
  return useMutation(
    async ({ id, payload }: CreateGroupingMutationProps) => {
      return client.updateGrouping(id, payload);
    },
    {
      onSuccess: (data, variables, context) => {
        queryClient.invalidateQueries('get-groupings-paginate');
      },
    },
  );
}
