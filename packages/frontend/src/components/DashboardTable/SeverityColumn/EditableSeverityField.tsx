import React from 'react';
import { Box, IconButton, TextField } from '@mui/material';
import { Save as SaveIcon, Close as CloseIcon } from '@mui/icons-material';

import { Grouping } from '../../../interfaces';

import { ColumnType } from '../columns';
import { useUpdateGrouping } from './hooks';

interface Props {
  data: Grouping;
  onClose: () => void;
}

export default function EditableSeverityField({ data, onClose }: Props) {
  const [value, setValue] = React.useState(data[ColumnType.Severity]);
  const updateGrouping: any = useUpdateGrouping();

  // React.useCallback(() => {
  //   console.log(updateGrouping.isSuccess);
  //   if (updateGrouping.isSuccess) {
  //     onClose();
  //   }
  // }, [updateGrouping, onClose]);

  const handleSave = React.useCallback(() => {
    updateGrouping.mutate(
      {
        id: data[ColumnType.Id],
        payload: {
          [ColumnType.Severity]: value,
        },
      },
      {
        onSuccess: () => onClose(),
      },
    );
  }, [data, updateGrouping, value, onClose]);

  return (
    <Box display="flex">
      {updateGrouping.isSuccess && <h3>lksdnf</h3>}
      <TextField
        id={ColumnType.Severity}
        autoComplete="chrome-off"
        label="Severity"
        variant="outlined"
        size="small"
        value={value}
        onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
          setValue(event.target.value);
        }}
      />
      <IconButton size="small" onClick={() => handleSave()}>
        <SaveIcon />
      </IconButton>
      <IconButton size="small" onClick={() => onClose()}>
        <CloseIcon />
      </IconButton>
    </Box>
  );
}
