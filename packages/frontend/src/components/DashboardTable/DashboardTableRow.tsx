import React from 'react';
import { TableCell, TableRow } from '@mui/material';

import { Grouping } from '../../interfaces/grouping.interface';
import ExpanderRow from './ExpanderRow';
import { columns } from './columns';

interface Props {
  grouping: Grouping;
}

export default function DashboardTableRow({ grouping }: Props) {
  const [open, setOpen] = React.useState(false);

  return (
    <React.Fragment>
      <TableRow hover>
        {columns.map((column, idx) => (
          <TableCell key={idx}>
            {column.format
              ? column.format(grouping, {
                  open,
                  onToggle: () => setOpen(!open),
                })
              : grouping[column.id]}
          </TableCell>
        ))}
      </TableRow>
      <ExpanderRow grouping={grouping} open={open} />
    </React.Fragment>
  );
}
