import { IconButton } from '@mui/material';
import {
  KeyboardArrowDown as KeyboardArrowDownIcon,
  KeyboardArrowUp as KeyboardArrowUpIcon,
} from '@mui/icons-material';

import { formatDate } from '../../utilities/format';
import { Grouping } from '../../interfaces';
import { Column } from '../../types/table';
import { statusLabel, Status } from '../../types/findings';
import SeverityColumn from './SeverityColumn';

export enum ColumnType {
  Id = 'id',
  Description = 'description',
  GroupedFindingCreated = 'groupedFindingCreated',
  GroupingKey = 'groupingKey',
  GroupingType = 'groupingType',
  Owner = 'owner',
  Progress = 'progress',
  SecurityAnalyst = 'securityAnalyst',
  Severity = 'severity',
  Sla = 'sla',
  Status = 'statusStr',
  Workflow = 'workflow',
}

export const columns: Column<ColumnType, Grouping>[] = [
  {
    id: ColumnType.Id,
    //NOTE: Empty header label for expand/collapse functionality
    format: (item, { open, onToggle }) => {
      return (
        <IconButton
          aria-label="expand row"
          size="small"
          onClick={() => onToggle()}
        >
          {open ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
        </IconButton>
      );
    },
  },
  {
    id: ColumnType.Id,
    label: 'ID',
    enableSort: true,
  },
  {
    id: ColumnType.GroupedFindingCreated,
    label: 'Created',
    minWidth: 160,
    format: (item) => formatDate(item[ColumnType.GroupedFindingCreated]),
    enableSort: true,
  },
  {
    id: ColumnType.GroupingType,
    label: 'Group Type',
  },
  {
    id: ColumnType.Owner,
    label: 'Owner',
  },
  {
    id: ColumnType.Progress,
    label: 'Progress',
    format: (item) => {
      return `${Math.round(item[ColumnType.Progress] * 100)}%`;
    },
    enableSort: true,
  },
  {
    id: ColumnType.SecurityAnalyst,
    label: 'Security Analyst',
  },
  {
    id: ColumnType.Severity,
    label: 'Severity',
    enableSort: true,
    format: (item) => {
      return <SeverityColumn data={item} />;
    },
  },
  {
    id: ColumnType.Sla,
    label: 'SLA',
    minWidth: 160,
    format: (item) => formatDate(item[ColumnType.Sla]),
  },
  {
    id: ColumnType.Status,
    label: 'Status',
    minWidth: 90,
    format: (item) => {
      const statusKey: Status = item[ColumnType.Status] as Status;
      return statusLabel[statusKey];
    },
    enableSort: true,
  },
  {
    id: ColumnType.Workflow,
    label: 'Workflow',
    enableSort: true,
  },
];
