import { Box, Modal, IconButton, Typography, Paper } from '@mui/material';
import { Close as CloseIcon } from '@mui/icons-material';

import { colors } from '../utilities/styleList';
import { useModal } from '../context/modal';

interface Props {
  label: string;
  children: React.ReactNode;
  width?: string;
  maxWidth?: string;
  fullscreen?: boolean;
}

export default function CustomModal({
  label,
  children,
  width = '600px',
  maxWidth = '90vw',
  fullscreen = false,
}: Props) {
  const modal = useModal();

  return (
    <Modal
      open={true}
      onClose={() => {
        modal.dismiss();
      }}
      disableRestoreFocus
      sx={{
        display: 'flex',
        alignItems: fullscreen ? 'stretch' : 'center',
        justifyContent: fullscreen ? 'stretch' : 'center',
        padding: fullscreen ? 0 : 2,
      }}
    >
      <Paper
        elevation={fullscreen ? 0 : 8}
        sx={{
          width: fullscreen ? '100vw' : width,
          maxWidth: fullscreen ? 'none' : maxWidth,
          maxHeight: fullscreen ? '100vh' : '90vh',
          height: fullscreen ? '100vh' : 'auto',
          display: 'flex',
          flexDirection: 'column',
          outline: 'none',
          borderRadius: fullscreen ? 0 : undefined,
        }}
      >
        {/* Header */}
        <Box
          display="flex"
          flexDirection="row"
          padding="5px 18px 6px 30px"
          justifyContent="space-between"
          borderBottom={`1px solid ${colors.silver}`}
          sx={{ flexShrink: 0 }}
        >
          <Box marginTop="12px">
            <Typography variant="h6">{label}</Typography>
          </Box>

          <IconButton
            aria-label="close"
            onClick={() => {
              modal.dismiss();
            }}
          >
            <CloseIcon />
          </IconButton>
        </Box>

        {/* Content */}
        <Box
          display="flex"
          margin="30px"
          flexDirection="column"
          flex="1 1 auto"
          sx={{ overflowY: 'auto' }}
        >
          {children}
        </Box>
      </Paper>
    </Modal>
  );
}
