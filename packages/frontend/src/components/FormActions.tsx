import { useModal } from '../context/modal';
import { Box, Button, CircularProgress } from '@mui/material';

interface Props {
  label?: string;
  isDisabled?: boolean;
  isLoading?: boolean;
  onSubmit?: () => void;
  onCancel?: () => void;
}

export default function FormActions({
  label,
  isDisabled = false,
  isLoading = false,
  onSubmit,
  onCancel,
}: Props) {
  const modal = useModal();

  return (
    <Box marginTop="25px">
      <Button
        variant="outlined"
        size="small"
        onClick={() => {
          if (onCancel != null) {
            onCancel();
          } else {
            modal.dismiss();
          }
        }}
      >
        Cancel
      </Button>
      {label != null && onSubmit != null && (
        <Button
          style={{ marginLeft: '15px' }}
          variant="contained"
          size="small"
          color="primary"
          onClick={() => onSubmit()}
          disabled={isDisabled}
        >
          {isLoading && (
            <Box display="flex">
              <CircularProgress size={20} style={{ color: '#FFFFFF' }} />
            </Box>
          )}
          {!isLoading && <Box display="flex">{label}</Box>}
        </Button>
      )}
    </Box>
  );
}
