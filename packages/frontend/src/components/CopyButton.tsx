import React from 'react';
import { Button, IconButton, Tooltip } from '@mui/material';
import makeStyles from '@mui/styles/makeStyles';
import { ContentCopy as ContentCopyIcon } from '@mui/icons-material';

import { useSnackbar } from '../context/snackbar';
import Snackbar from './Snackbar';

const useStyles: any = makeStyles({
  iconButton: {
    width: '32px',
    height: '32px',
    fontSize: '1.2rem',
  },
});

interface Props {
  title: string;
  label: string;
  subLabel?: string;
  copyValue: string;
  isIconButton?: boolean;
}

export default function CopyButton({
  title,
  label,
  subLabel,
  copyValue,
  isIconButton = true,
}: Props) {
  const classes = useStyles();
  const snackbar = useSnackbar();

  const handleButtonClick = async () => {
    await navigator.clipboard.writeText(copyValue);
    snackbar.appoint(
      <Snackbar
        label={label}
        subLabel={subLabel}
        type="info"
        autoHideDuration={60000}
      />,
    );
  };

  if (isIconButton === true) {
    return (
      <Tooltip title={title}>
        <IconButton
          classes={{
            root: classes.iconButton,
          }}
          aria-label="copy"
          onClick={handleButtonClick}
        >
          <ContentCopyIcon />
        </IconButton>
      </Tooltip>
    );
  }

  return (
    <Button
      style={{
        letterSpacing: 'normal',
        padding: '6px 16px',
      }}
      color="primary"
      //@ts-ignore
      startIcon={<ContentCopyIcon />}
      onClick={handleButtonClick}
    >
      {title}
    </Button>
  );
}
