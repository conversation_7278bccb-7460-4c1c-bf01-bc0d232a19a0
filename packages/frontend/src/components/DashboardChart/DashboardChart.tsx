import { Box } from '@mui/material';
import { <PERSON><PERSON><PERSON>, Pie, ResponsiveContainer, Cell } from 'recharts';

import { useGroupingsSeverityCounts } from './hooks';
import { colors } from '../../utilities/styleList';

import { Severity } from '../../interfaces/grouping.interface';
import ChartLabels from './ChartLabels';

export const colorsLookup = {
  [Severity.Critical]: colors.wildWatermelon,
  [Severity.High]: colors.neonCarrot,
  [Severity.Medium]: colors.goldenTainoi,
  [Severity.Low]: colors.pictonBlue,
};

export default function DashboardChart() {
  const { data } = useGroupingsSeverityCounts();

  if (data == null) return null;

  return (
    <Box display="flex">
      <Box display="flex" width="300px" height="300px">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart width={300} height={300}>
            <Pie
              data={data}
              dataKey="count"
              cx="50%"
              cy="50%"
              outerRadius={100}
              labelLine={false}
            >
              {data.map((item, idx) => (
                <Cell
                  key={idx}
                  fill={colorsLookup[item.severity as Severity]}
                />
              ))}
            </Pie>
          </PieChart>
        </ResponsiveContainer>
      </Box>

      <ChartLabels data={data} />
    </Box>
  );
}
