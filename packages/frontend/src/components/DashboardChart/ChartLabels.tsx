import { Box } from '@mui/material';

import {
  Severity,
  GroupingSeverityCounts,
} from '../../interfaces/grouping.interface';

import { colorsLookup } from './DashboardChart';

interface Props {
  data: GroupingSeverityCounts[];
}

export default function ChartLabels({ data }: Props) {
  return (
    <Box
      display="flex"
      flexDirection="column"
      width="150px"
      justifyContent="center"
    >
      <Box fontSize="16px" marginBottom="10px">
        Findings counts by severity
      </Box>
      {data.map((item, idx) => (
        <Box display="flex" key={idx} marginBottom="10px" alignItems="center">
          <Box
            display="flex"
            width="40px"
            height="11px"
            bgcolor={colorsLookup[item.severity as Severity]}
          ></Box>
          <Box marginLeft="10px">
            {item.severity} - {item.count}
          </Box>
        </Box>
      ))}
    </Box>
  );
}
