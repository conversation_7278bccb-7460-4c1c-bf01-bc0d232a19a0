import {
  AppB<PERSON>,
  Toolbar,
  Container,
  Link,
  Typography,
  Box,
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import AnalyticsIcon from '@mui/icons-material/Analytics';

function ProjectAppBar() {
  return (
    <AppBar position="sticky">
      <Container maxWidth="xl">
        <Toolbar disableGutters>
          <Box marginRight="10px">
            <Link component={RouterLink} to="/">
              <AnalyticsIcon sx={{ color: '#FFFFFF' }} />
            </Link>
          </Box>

          <Typography
            variant="h6"
            noWrap
            component="a"
            href="/"
            sx={{
              mr: 2,
              display: { xs: 'none', md: 'flex' },
              fontFamily: 'monospace',
              fontWeight: 700,
              letterSpacing: '.3rem',
              color: 'inherit',
              textDecoration: 'none',
            }}
          >
            PROJECT
          </Typography>
        </Toolbar>
      </Container>
    </AppBar>
  );
}
export default ProjectAppBar;
