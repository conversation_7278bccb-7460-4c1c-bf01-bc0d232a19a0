import { AppBar, Toolbar, Typography, Container } from '@mui/material';

import Logo from '../icons/Silk-Logo-White.svg';

function ProjectAppBar() {
  return (
    <AppBar position="sticky">
      <Container maxWidth="xl">
        <Toolbar disableGutters>
          <img src={Logo} style={{ width: 81, height: 36 }} alt="Silk Logo" />
          <Typography
            variant="h5"
            noWrap
            component="a"
            href="#dashboard"
            sx={{
              fontWeight: 700,
              color: 'inherit',
              textDecoration: 'none',
            }}
          >
            Dashboard
          </Typography>
        </Toolbar>
      </Container>
    </AppBar>
  );
}
export default ProjectAppBar;
