import React, { useState, useRef, useEffect } from 'react';
import { useCustomers, Customer } from './hooks';
import CustomerDrawer from './CustomerDrawer';

interface CustomerDropdownProps {
  selectedCustomer?: Customer | null;
  onCustomerSelect: (customer: Customer | null) => void;
  placeholder?: string;
}

export default function CustomerDropdown({ 
  selectedCustomer, 
  onCustomerSelect, 
  placeholder = "Add customer" 
}: CustomerDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);
  
  const { data: customers = [], isLoading } = useCustomers(searchTerm);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleCustomerCreated = (newCustomer: Customer) => {
    onCustomerSelect(newCustomer);
    setIsDrawerOpen(false);
  };

  const handleAddNewClick = () => {
    setIsOpen(false);
    setIsDrawerOpen(true);
  };

  const handleCustomerClick = (customer: Customer) => {
    onCustomerSelect(customer);
    setIsOpen(false);
  };

  const displayText = selectedCustomer ? selectedCustomer.name : placeholder;
  const hasCustomers = customers.length > 0;

  return (
    <>
      <div className="relative" ref={dropdownRef}>
        <button
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          className="w-full flex items-center justify-between px-3 py-2 border border-gray-300 rounded-md bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <span className={selectedCustomer ? 'text-gray-900' : 'text-gray-500'}>
            {displayText}
          </span>
          <svg
            className={`w-5 h-5 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>

        {isOpen && (
          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg">
            {/* Search input when there are customers */}
            {hasCustomers && (
              <div className="p-2 border-b">
                <input
                  type="text"
                  placeholder="Search customers..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  autoFocus
                />
              </div>
            )}

            <div className="max-h-60 overflow-y-auto">
              {/* Add New option */}
              <button
                type="button"
                onClick={handleAddNewClick}
                className="w-full flex items-center px-3 py-2 text-left hover:bg-gray-50 focus:outline-none focus:bg-gray-50"
              >
                <span className="text-green-600 mr-2">+</span>
                <span className="text-gray-700">Add new</span>
              </button>

              {/* Customer list */}
              {isLoading ? (
                <div className="px-3 py-2 text-gray-500">Loading...</div>
              ) : customers.length > 0 ? (
                customers.map((customer) => (
                  <button
                    key={customer.id}
                    type="button"
                    onClick={() => handleCustomerClick(customer)}
                    className="w-full flex items-center px-3 py-2 text-left hover:bg-gray-50 focus:outline-none focus:bg-gray-50"
                  >
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">{customer.name}</div>
                      {customer.email && (
                        <div className="text-sm text-gray-500">{customer.email}</div>
                      )}
                    </div>
                  </button>
                ))
              ) : searchTerm ? (
                <div className="px-3 py-2 text-gray-500">No customers found</div>
              ) : null}
            </div>
          </div>
        )}
      </div>

      <CustomerDrawer
        isOpen={isDrawerOpen}
        onClose={() => setIsDrawerOpen(false)}
        onCustomerCreated={handleCustomerCreated}
      />
    </>
  );
}
