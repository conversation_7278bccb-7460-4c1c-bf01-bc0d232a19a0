import { useMutation, useQuery, useQueryClient } from 'react-query';
import { ProjectClient } from '../../clients/ProjectClient';

const client = new ProjectClient();

export interface Customer {
  id: number;
  name: string;
  email?: string;
  ccBcc?: string;
  billingStreetAddress1?: string;
  billingStreetAddress2?: string;
  billingCity?: string;
  billingState?: string;
  billingZipCode?: string;
  billingCountry?: string;
  shippingStreetAddress1?: string;
  shippingStreetAddress2?: string;
  shippingCity?: string;
  shippingState?: string;
  shippingZipCode?: string;
  shippingCountry?: string;
  sameAsBillingAddress: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CustomerFormData {
  name: string;
  email?: string;
  ccBcc?: string;
  billingStreetAddress1?: string;
  billingStreetAddress2?: string;
  billingCity?: string;
  billingState?: string;
  billingZipCode?: string;
  billingCountry?: string;
  shippingStreetAddress1?: string;
  shippingStreetAddress2?: string;
  shippingCity?: string;
  shippingState?: string;
  shippingZipCode?: string;
  shippingCountry?: string;
  sameAsBillingAddress: boolean;
}

export const useCreateCustomer = () => {
  const queryClient = useQueryClient();

  return useMutation(
    (customerData: CustomerFormData) => client.createCustomer(customerData),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('get-customers');
      },
      onError: (error) => {
        console.error('Error creating customer:', error);
      },
    },
  );
};

export const useCustomers = (search?: string) => {
  return useQuery(['get-customers', search], () => client.getCustomers(search), {
    onError: (error) => {
      console.error('Error fetching customers:', error);
    },
  });
};

export const useCustomer = (id: number) => {
  return useQuery(['get-customer', id], () => client.getCustomer(id), {
    enabled: !!id,
    onError: (error) => {
      console.error('Error fetching customer:', error);
    },
  });
};

export const useUpdateCustomer = () => {
  const queryClient = useQueryClient();

  return useMutation(
    ({ id, data }: { id: number; data: Partial<CustomerFormData> }) =>
      client.updateCustomer(id, data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('get-customers');
      },
      onError: (error) => {
        console.error('Error updating customer:', error);
      },
    },
  );
};

export const useDeleteCustomer = () => {
  const queryClient = useQueryClient();

  return useMutation((id: number) => client.deleteCustomer(id), {
    onSuccess: () => {
      queryClient.invalidateQueries('get-customers');
    },
    onError: (error) => {
      console.error('Error deleting customer:', error);
    },
  });
};
