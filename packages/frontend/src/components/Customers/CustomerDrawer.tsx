import React, { useState } from 'react';
import { useCreateCustomer, CustomerFormData } from './hooks';

interface CustomerDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onCustomerCreated?: (customer: any) => void;
}

export default function CustomerDrawer({ isOpen, onClose, onCustomerCreated }: CustomerDrawerProps) {
  const [formData, setFormData] = useState<CustomerFormData>({
    name: '',
    email: '',
    ccBcc: '',
    billingStreetAddress1: '',
    billingStreetAddress2: '',
    billingCity: '',
    billingState: '',
    billingZipCode: '',
    billingCountry: '',
    shippingStreetAddress1: '',
    shippingStreetAddress2: '',
    shippingCity: '',
    shippingState: '',
    shippingZipCode: '',
    shippingCountry: '',
    sameAsBillingAddress: false,
  });

  const [expandedSections, setExpandedSections] = useState({
    nameAndContact: true,
    addresses: true,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const createCustomerMutation = useCreateCustomer();

  const handleInputChange = (field: keyof CustomerFormData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: '',
      }));
    }

    // If sameAsBillingAddress is checked, copy billing to shipping
    if (field === 'sameAsBillingAddress' && value === true) {
      setFormData(prev => ({
        ...prev,
        sameAsBillingAddress: true,
        shippingStreetAddress1: prev.billingStreetAddress1,
        shippingStreetAddress2: prev.billingStreetAddress2,
        shippingCity: prev.billingCity,
        shippingState: prev.billingState,
        shippingZipCode: prev.billingZipCode,
        shippingCountry: prev.billingCountry,
      }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Customer name is required';
    }

    if (formData.email && formData.email.trim() && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Invalid email format';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      const result = await createCustomerMutation.mutateAsync(formData);
      onCustomerCreated?.(result);
      onClose();
      
      // Reset form
      setFormData({
        name: '',
        email: '',
        ccBcc: '',
        billingStreetAddress1: '',
        billingStreetAddress2: '',
        billingCity: '',
        billingState: '',
        billingZipCode: '',
        billingCountry: '',
        shippingStreetAddress1: '',
        shippingStreetAddress2: '',
        shippingCity: '',
        shippingState: '',
        shippingZipCode: '',
        shippingCountry: '',
        sameAsBillingAddress: false,
      });
      setErrors({});
    } catch (error) {
      console.error('Error creating customer:', error);
    }
  };

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      <div className="absolute inset-0 bg-black bg-opacity-50" onClick={onClose} />
      
      <div className="absolute right-0 top-0 h-full w-96 bg-white shadow-xl">
        <div className="flex h-full flex-col">
          {/* Header */}
          <div className="flex items-center justify-between border-b px-6 py-4">
            <h2 className="text-lg font-semibold">Add Customer</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="flex-1 overflow-y-auto">
            <div className="p-6 space-y-6">
              
              {/* Name and Contact Section */}
              <div className="border rounded-lg">
                <button
                  type="button"
                  onClick={() => toggleSection('nameAndContact')}
                  className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50"
                >
                  <div className="flex items-center space-x-2">
                    <span className="text-gray-600">👤</span>
                    <span className="font-medium">Name and contact</span>
                  </div>
                  <span className={`transform transition-transform ${expandedSections.nameAndContact ? 'rotate-180' : ''}`}>
                    ▼
                  </span>
                </button>
                
                {expandedSections.nameAndContact && (
                  <div className="px-4 pb-4 space-y-4">
                    <div>
                      <input
                        type="text"
                        placeholder="Customer name"
                        value={formData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                          errors.name ? 'border-red-500' : 'border-gray-300'
                        }`}
                      />
                      {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
                    </div>
                    
                    <div>
                      <input
                        type="email"
                        placeholder="Enter customer email"
                        value={formData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                          errors.email ? 'border-red-500' : 'border-gray-300'
                        }`}
                      />
                      {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
                    </div>
                    
                    <div>
                      <label className="text-blue-600 text-sm font-medium">Cc/Bcc</label>
                      <input
                        type="text"
                        placeholder="Additional email addresses"
                        value={formData.ccBcc}
                        onChange={(e) => handleInputChange('ccBcc', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mt-1"
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* Addresses Section */}
              <div className="border rounded-lg">
                <button
                  type="button"
                  onClick={() => toggleSection('addresses')}
                  className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50"
                >
                  <div className="flex items-center space-x-2">
                    <span className="text-gray-600">📍</span>
                    <span className="font-medium">Addresses</span>
                  </div>
                  <span className={`transform transition-transform ${expandedSections.addresses ? 'rotate-180' : ''}`}>
                    ▼
                  </span>
                </button>
                
                {expandedSections.addresses && (
                  <div className="px-4 pb-4 space-y-6">
                    {/* Billing Address */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-3">Billing address</h4>
                      <div className="space-y-3">
                        <div className="grid grid-cols-2 gap-3">
                          <div>
                            <label className="text-sm text-gray-600">Street address 1</label>
                            <input
                              type="text"
                              value={formData.billingStreetAddress1}
                              onChange={(e) => handleInputChange('billingStreetAddress1', e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mt-1"
                            />
                          </div>
                          <div>
                            <label className="text-sm text-gray-600">Street address 2</label>
                            <input
                              type="text"
                              value={formData.billingStreetAddress2}
                              onChange={(e) => handleInputChange('billingStreetAddress2', e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mt-1"
                            />
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-3">
                          <div>
                            <label className="text-sm text-gray-600">City</label>
                            <input
                              type="text"
                              value={formData.billingCity}
                              onChange={(e) => handleInputChange('billingCity', e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mt-1"
                            />
                          </div>
                          <div>
                            <label className="text-sm text-gray-600">State</label>
                            <input
                              type="text"
                              value={formData.billingState}
                              onChange={(e) => handleInputChange('billingState', e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mt-1"
                            />
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-3">
                          <div>
                            <label className="text-sm text-gray-600">ZIP code</label>
                            <input
                              type="text"
                              value={formData.billingZipCode}
                              onChange={(e) => handleInputChange('billingZipCode', e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mt-1"
                            />
                          </div>
                          <div>
                            <label className="text-sm text-gray-600">Country</label>
                            <input
                              type="text"
                              value={formData.billingCountry}
                              onChange={(e) => handleInputChange('billingCountry', e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mt-1"
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Shipping Address */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-3">Shipping address</h4>
                      
                      <div className="flex items-center space-x-2 mb-3">
                        <input
                          type="checkbox"
                          id="sameAsBilling"
                          checked={formData.sameAsBillingAddress}
                          onChange={(e) => handleInputChange('sameAsBillingAddress', e.target.checked)}
                          className="w-4 h-4 text-green-600 border-gray-300 rounded focus:ring-green-500"
                        />
                        <label htmlFor="sameAsBilling" className="text-sm text-gray-700">
                          Same as billing address
                        </label>
                      </div>
                      
                      {!formData.sameAsBillingAddress && (
                        <div className="space-y-3">
                          <div className="grid grid-cols-2 gap-3">
                            <div>
                              <label className="text-sm text-gray-600">Street address 1</label>
                              <input
                                type="text"
                                value={formData.shippingStreetAddress1}
                                onChange={(e) => handleInputChange('shippingStreetAddress1', e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mt-1"
                              />
                            </div>
                            <div>
                              <label className="text-sm text-gray-600">Street address 2</label>
                              <input
                                type="text"
                                value={formData.shippingStreetAddress2}
                                onChange={(e) => handleInputChange('shippingStreetAddress2', e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mt-1"
                              />
                            </div>
                          </div>
                          
                          <div className="grid grid-cols-2 gap-3">
                            <div>
                              <label className="text-sm text-gray-600">City</label>
                              <input
                                type="text"
                                value={formData.shippingCity}
                                onChange={(e) => handleInputChange('shippingCity', e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mt-1"
                              />
                            </div>
                            <div>
                              <label className="text-sm text-gray-600">State</label>
                              <input
                                type="text"
                                value={formData.shippingState}
                                onChange={(e) => handleInputChange('shippingState', e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mt-1"
                              />
                            </div>
                          </div>
                          
                          <div className="grid grid-cols-2 gap-3">
                            <div>
                              <label className="text-sm text-gray-600">ZIP code</label>
                              <input
                                type="text"
                                value={formData.shippingZipCode}
                                onChange={(e) => handleInputChange('shippingZipCode', e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mt-1"
                              />
                            </div>
                            <div>
                              <label className="text-sm text-gray-600">Country</label>
                              <input
                                type="text"
                                value={formData.shippingCountry}
                                onChange={(e) => handleInputChange('shippingCountry', e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mt-1"
                              />
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Footer */}
            <div className="border-t px-6 py-4 bg-gray-50">
              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={onClose}
                  className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={createCustomerMutation.isLoading}
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  {createCustomerMutation.isLoading ? 'Saving...' : 'Save Customer'}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
