import { Box, Drawer, IconButton, Typography } from '@mui/material';
import { Close as CloseIcon } from '@mui/icons-material';

import { colors } from '../utilities/styleList';
import { useModal } from '../context/modal';

interface Props {
  label: string;
  children: React.ReactNode;
  width?: string;
}

export default function CustomDrawer({
  label,
  children,
  width = '600px',
}: Props) {
  const modal = useModal();
  return (
    <Drawer
      anchor="right"
      open={true}
      onClose={() => {
        modal.dismiss();
      }}
    >
      <Box display="flex" width={width} flexDirection="column" flex="1 1 auto">
        <Box
          display="flex"
          flexDirection="row"
          padding="5px 18px 6px 30px"
          justifyContent="space-between"
          borderBottom={`1px solid ${colors.silver}`}
        >
          <Box marginTop="12px">
            <Typography variant="h6">{label}</Typography>
          </Box>

          <IconButton
            aria-label="close"
            onClick={() => {
              modal.dismiss();
            }}
          >
            <CloseIcon />
          </IconButton>
        </Box>

        <Box
          display="flex"
          margin="30px"
          flexDirection="column"
          flex="1 1 auto"
        >
          {children}
        </Box>
      </Box>
    </Drawer>
  );
}
