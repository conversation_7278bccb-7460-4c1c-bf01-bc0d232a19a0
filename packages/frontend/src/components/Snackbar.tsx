import React from 'react';
import { Box, IconButton, Snackbar as Mui<PERSON>nackbar } from '@mui/material';
import {
  Close as CloseIcon,
  Check as CheckIcon,
  WarningAmber as WarningAmberIcon,
  ErrorOutline as ErrorOutlineIcon,
} from '@mui/icons-material';

import { useSnackbar } from '../context/snackbar';
import { colors } from '../utilities/styleList';

type SnackbarType = 'success' | 'error' | 'info' | 'warning';

interface CustomAlertProps {
  label: string;
  subLabel?: string;
  type: SnackbarType;
  onClose: () => void;
}

const CustomAlertIcon = ({ type }: { type: SnackbarType }) => {
  switch (type) {
    case 'success':
      return <CheckIcon fontSize="large" />;
    case 'warning':
      return <WarningAmberIcon fontSize="large" />;
    case 'info':
      return <ErrorOutlineIcon fontSize="large" />;
    case 'error':
      return <ErrorOutlineIcon fontSize="large" />;
    default:
      return <ErrorOutlineIcon fontSize="large" />;
  }
};

const getAlertColor = (type: SnackbarType) => {
  switch (type) {
    case 'success':
      return colors.aquamarine;
    case 'warning':
      return colors.texasRose;
    case 'info':
      return colors.malibu;
    case 'error':
      return colors.carnation;
    default:
      return colors.malibu;
  }
};

const CustomAlert = ({ label, type, subLabel, onClose }: CustomAlertProps) => {
  return (
    <Box
      display="flex"
      padding="15px"
      style={{ backgroundColor: getAlertColor(type) }}
      color={type === 'error' ? '#ffffff' : undefined}
      borderRadius="5px"
    >
      <Box display="flex" marginTop="5px">
        <CustomAlertIcon type={type} />
      </Box>
      <Box
        display="flex"
        flex="1 1 auto"
        flexDirection="column"
        marginLeft="20px"
      >
        <Box display="flex" fontWeight="600">
          {label}
        </Box>
        {subLabel != null && (
          <Box display="flex" marginTop="5px">
            {subLabel}
          </Box>
        )}
      </Box>
      <Box display="flex">
        <IconButton
          size="large"
          aria-label="dismiss"
          onClick={() => onClose()}
          style={{
            color: type === 'error' ? '#ffffff' : undefined,
            height: '40px',
          }}
        >
          <CloseIcon />
        </IconButton>
      </Box>
    </Box>
  );
};

interface SnackbarProps {
  label: string;
  subLabel?: string;
  children?: React.ReactNode;
  shouldAutoHide?: boolean;
  autoHideDuration?: number;
  type?: SnackbarType;
  // injected by modal provider
  resultHook?: {
    resolve: (payload?: string) => void;
    reject: () => void;
  };
}

export default function Snackbar({
  label,
  subLabel,
  children,
  resultHook,
  shouldAutoHide,
  autoHideDuration = 5000,
  type = 'info',
}: SnackbarProps) {
  const snackbar = useSnackbar();

  const customShouldAutoHide =
    shouldAutoHide === true || type === 'success' || type === 'info';

  React.useEffect(() => {
    if (customShouldAutoHide) {
      let timer = setTimeout(() => snackbar.dismiss(), autoHideDuration);

      return () => {
        clearTimeout(timer);
      };
    }

    // eslint-disable-next-line
  }, [customShouldAutoHide]);

  return (
    <MuiSnackbar
      anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      autoHideDuration={
        customShouldAutoHide === true ? autoHideDuration : undefined
      }
      open={true}
      onClose={() => {}}
    >
      <Box
        display="flex"
        flexDirection="column"
        minWidth="350px"
        maxWidth="500px"
      >
        {children != null && <Box display="flex">{children}</Box>}
        {label != null && (
          <CustomAlert
            label={label}
            subLabel={subLabel}
            type={type}
            onClose={() => {
              snackbar.dismiss();
            }}
          />
        )}
      </Box>
    </MuiSnackbar>
  );
}
