import React from 'react';
import {
  FormControl,
  InputLabel,
  Select,
  FormHelperText,
  SelectChangeEvent,
} from '@mui/material';

interface Props {
  label: string;
  helperText?: string;
  id: string;
  onChange: (event: SelectChangeEvent<unknown>) => void;
  value: any;
  children: React.ReactNode;
  isDisabled?: boolean;
  renderValue?: (selected: any) => string;
  autoFocus?: boolean;
  menuProps?: any;
}

export default function CustomSelect({
  id,
  label,
  helperText = '',
  onChange,
  value,
  children,
  isDisabled,
  renderValue,
  autoFocus,
  menuProps,
}: Props) {
  return (
    <FormControl fullWidth size="small">
      <InputLabel>{label}</InputLabel>
      <Select
        id={id}
        value={value}
        autoFocus={autoFocus}
        onChange={onChange}
        label={label}
        disabled={isDisabled}
        //@ts-ignore
        renderValue={renderValue}
        MenuProps={menuProps}
      >
        {children}
      </Select>

      <FormHelperText>{helperText}</FormHelperText>
    </FormControl>
  );
}
