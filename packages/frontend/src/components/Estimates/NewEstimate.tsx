import { Helmet } from 'react-helmet-async'
import EstimateForm from './EstimateForm'
import { Box } from '@mui/material'

export default function NewEstimate() {
  const handleSubmit = (values: any) => {
    console.log('Estimate submitted:', values)
    // Here you would typically send the data to your API
    // For now, we'll just log it to the console
    alert('Estimate saved successfully!')
  }

  return (
    <Box sx={{ margin: '20px' }}>
      <Helmet>
        <title>New Estimate | Project</title>
      </Helmet>
      <EstimateForm onSubmit={handleSubmit} />
    </Box>
  )
}
