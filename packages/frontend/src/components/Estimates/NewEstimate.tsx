import EstimateForm from './EstimateForm';
import CustomModal from '../CustomModal';

export default function NewEstimate() {
  const handleSubmit = (values: any) => {
    console.log('Estimate submitted:', values);
    // Here you would typically send the data to your API
    // For now, we'll just log it to the console
    alert('Estimate saved successfully!');
  };

  return (
    <CustomModal label="New Estimate" fullscreen>
      <EstimateForm onSubmit={handleSubmit} />
    </CustomModal>
  );
}
