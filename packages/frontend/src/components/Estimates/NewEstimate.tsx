import { Helmet } from 'react-helmet-async';
import EstimateForm from './EstimateForm';
import CustomModal from '../CustomModal';
import { useCreateEstimate } from './hooks';
import { useModal } from '../../context/modal';

export default function NewEstimate() {
  const modal = useModal();
  const createEstimateMutation = useCreateEstimate();

  const handleSubmit = async (values: any) => {
    try {
      // Filter out line items with empty types and transform data
      const validLineItems = values.lineItems
        .filter((item: any) => item.type && item.type !== '')
        .map((item: any) => ({
          type: item.type as 'Labor' | 'Materials' | 'Equipment',
          item: item.item,
          units: item.units,
          time: item.time || undefined,
          rate: item.rate,
          margin: item.margin,
        }));

      if (validLineItems.length === 0) {
        alert('Please add at least one valid line item.');
        return;
      }

      const payload = {
        estimate: {
          name: `Estimate ${new Date().toLocaleDateString()}`,
          description: 'Generated from estimate form',
        },
        lineItems: validLineItems,
      };

      await createEstimateMutation.mutateAsync({ payload });

      // Show success message and close modal
      alert('Estimate saved successfully!');
      modal.dismiss();
    } catch (error) {
      console.error('Error saving estimate:', error);
      alert('Failed to save estimate. Please try again.');
    }
  };

  return (
    <CustomModal label="New Estimate" fullscreen>
      <Helmet>
        <title>New Estimate | Project</title>
      </Helmet>
      <EstimateForm
        onSubmit={handleSubmit}
        isLoading={createEstimateMutation.isLoading}
      />
    </CustomModal>
  );
}
