import React, { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import EstimateForm from './EstimateForm';
import CustomModal from '../CustomModal';
import { useCreateEstimate } from './hooks';
import { useModal } from '../../context/modal';
import CustomerDropdown from '../Customers/CustomerDropdown';
import { Customer } from '../Customers/hooks';

export default function NewEstimate() {
  const modal = useModal();
  const createEstimateMutation = useCreateEstimate();
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(
    null,
  );

  const handleSubmit = async (values: any) => {
    try {
      // Filter out line items with empty types and transform data
      const validLineItems = values.lineItems
        .filter((item: any) => item.type && item.type !== '')
        .map((item: any) => ({
          type: item.type as 'Labor' | 'Materials' | 'Equipment',
          item: item.item,
          units: item.units,
          time: item.time || undefined,
          rate: item.rate,
          margin: item.margin,
        }));

      if (validLineItems.length === 0) {
        alert('Please add at least one valid line item.');
        return;
      }

      const payload = {
        estimate: {
          name: `Estimate ${new Date().toLocaleDateString()}`,
          description: 'Generated from estimate form',
          customerId: selectedCustomer?.id,
        },
        lineItems: validLineItems,
      };

      await createEstimateMutation.mutateAsync({ payload });

      // Show success message and close modal
      alert('Estimate saved successfully!');
      modal.dismiss();
    } catch (error) {
      console.error('Error saving estimate:', error);
      alert('Failed to save estimate. Please try again.');
    }
  };

  return (
    <CustomModal label="New Estimate" fullscreen>
      <Helmet>
        <title>New Estimate | Project</title>
      </Helmet>

      <div className="space-y-6">
        {/* Customer Selection */}
        <div className="bg-white p-6 rounded-lg border">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Customer</h3>
          <div className="max-w-md">
            <CustomerDropdown
              selectedCustomer={selectedCustomer}
              onCustomerSelect={setSelectedCustomer}
              placeholder="Add customer"
            />
          </div>

          {selectedCustomer && (
            <div className="mt-4 p-4 bg-gray-50 rounded-md">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900">
                    {selectedCustomer.name}
                  </h4>
                  {selectedCustomer.email && (
                    <p className="text-sm text-gray-600 mt-1">
                      {selectedCustomer.email}
                    </p>
                  )}

                  <div className="mt-3 grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <h5 className="font-medium text-gray-700">Bill to</h5>
                      <div className="text-gray-600 mt-1">
                        <div>{selectedCustomer.name}</div>
                        {selectedCustomer.billingStreetAddress1 && (
                          <div>{selectedCustomer.billingStreetAddress1}</div>
                        )}
                        {selectedCustomer.billingStreetAddress2 && (
                          <div>{selectedCustomer.billingStreetAddress2}</div>
                        )}
                        {(selectedCustomer.billingCity ||
                          selectedCustomer.billingState ||
                          selectedCustomer.billingZipCode) && (
                          <div>
                            {selectedCustomer.billingCity &&
                              selectedCustomer.billingCity}
                            {selectedCustomer.billingCity &&
                              selectedCustomer.billingState &&
                              ', '}
                            {selectedCustomer.billingState &&
                              selectedCustomer.billingState}
                            {selectedCustomer.billingZipCode &&
                              ` ${selectedCustomer.billingZipCode}`}
                          </div>
                        )}
                      </div>
                    </div>

                    <div>
                      <h5 className="font-medium text-gray-700">Ship to</h5>
                      <div className="text-gray-600 mt-1">
                        <div>{selectedCustomer.name}</div>
                        {selectedCustomer.sameAsBillingAddress ? (
                          <>
                            {selectedCustomer.billingStreetAddress1 && (
                              <div>
                                {selectedCustomer.billingStreetAddress1}
                              </div>
                            )}
                            {selectedCustomer.billingStreetAddress2 && (
                              <div>
                                {selectedCustomer.billingStreetAddress2}
                              </div>
                            )}
                            {(selectedCustomer.billingCity ||
                              selectedCustomer.billingState ||
                              selectedCustomer.billingZipCode) && (
                              <div>
                                {selectedCustomer.billingCity &&
                                  selectedCustomer.billingCity}
                                {selectedCustomer.billingCity &&
                                  selectedCustomer.billingState &&
                                  ', '}
                                {selectedCustomer.billingState &&
                                  selectedCustomer.billingState}
                                {selectedCustomer.billingZipCode &&
                                  ` ${selectedCustomer.billingZipCode}`}
                              </div>
                            )}
                          </>
                        ) : (
                          <>
                            {selectedCustomer.shippingStreetAddress1 && (
                              <div>
                                {selectedCustomer.shippingStreetAddress1}
                              </div>
                            )}
                            {selectedCustomer.shippingStreetAddress2 && (
                              <div>
                                {selectedCustomer.shippingStreetAddress2}
                              </div>
                            )}
                            {(selectedCustomer.shippingCity ||
                              selectedCustomer.shippingState ||
                              selectedCustomer.shippingZipCode) && (
                              <div>
                                {selectedCustomer.shippingCity &&
                                  selectedCustomer.shippingCity}
                                {selectedCustomer.shippingCity &&
                                  selectedCustomer.shippingState &&
                                  ', '}
                                {selectedCustomer.shippingState &&
                                  selectedCustomer.shippingState}
                                {selectedCustomer.shippingZipCode &&
                                  ` ${selectedCustomer.shippingZipCode}`}
                              </div>
                            )}
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                <button
                  type="button"
                  className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                  onClick={() => setSelectedCustomer(null)}
                >
                  Edit customer
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Estimate Form */}
        <EstimateForm
          onSubmit={handleSubmit}
          isLoading={createEstimateMutation.isLoading}
        />
      </div>
    </CustomModal>
  );
}
