import { Formik, FieldArray } from 'formik';
import {
  Box,
  TextField,
  Select,
  MenuItem,
  FormControl,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  IconButton,
  Typography,
} from '@mui/material';
import { Add as AddIcon, Delete as DeleteIcon } from '@mui/icons-material';
import { useModal } from '../../context/modal';
import FormActions from '../FormActions';

// Types and enums
enum LineItemType {
  Labor = 'Labor',
  Materials = 'Materials',
  Equipment = 'Equipment',
}

enum LaborItems {
  Digout = 'Digout',
  Paving = 'Paving',
}

enum MaterialItems {
  Asphalt = 'Asphalt',
  Concrete = 'Concrete',
  Gravel = 'Gravel',
  Sealcoating = 'Sealcoating',
}

enum EquipmentItems {
  Bobcat = 'Bobcat',
  Trucks = 'Trucks',
  Paver = 'Paver',
}

interface LineItem {
  type: LineItemType | '';
  item: string;
  units: string;
  time: string;
  rate: string;
  margin: string;
}

interface FormValues {
  lineItems: LineItem[];
}

interface Props {
  onSubmit: (values: FormValues) => void | Promise<void>;
  initialValues?: FormValues;
  isLoading?: boolean;
}

// Validation functions
interface LineItemErrors {
  type?: string;
  item?: string;
  units?: string;
  time?: string;
  rate?: string;
  margin?: string;
}

const validateLineItem = (lineItem: LineItem): LineItemErrors => {
  const errors: LineItemErrors = {};

  if (!lineItem.type) {
    errors.type = 'Type is required';
  }

  if (!lineItem.item) {
    errors.item = 'Item is required';
  }

  if (!lineItem.units) {
    errors.units = 'Units is required';
  }

  if (
    (lineItem.type === LineItemType.Labor ||
      lineItem.type === LineItemType.Equipment) &&
    !lineItem.time
  ) {
    errors.time = 'Time is required';
  }

  if (!lineItem.rate) {
    errors.rate = 'Rate is required';
  } else if (
    isNaN(parseFloat(lineItem.rate)) ||
    parseFloat(lineItem.rate) < 0
  ) {
    errors.rate = 'Rate must be a valid positive number';
  }

  if (!lineItem.margin) {
    errors.margin = 'Margin is required';
  } else if (
    isNaN(parseFloat(lineItem.margin)) ||
    parseFloat(lineItem.margin) < 0 ||
    parseFloat(lineItem.margin) >= 100
  ) {
    errors.margin = 'Margin must be a number between 0 and 99';
  }

  return errors;
};

const validateForm = (values: FormValues) => {
  const errors: any = {};

  if (!values.lineItems || values.lineItems.length === 0) {
    errors.lineItems = 'At least one line item is required';
  } else {
    const lineItemErrors = values.lineItems.map(validateLineItem);
    if (lineItemErrors.some((error) => Object.keys(error).length > 0)) {
      errors.lineItems = lineItemErrors;
    }
  }

  return errors;
};

// Helper functions
const getItemOptions = (type: LineItemType | '') => {
  switch (type) {
    case LineItemType.Labor:
      return Object.values(LaborItems);
    case LineItemType.Materials:
      return Object.values(MaterialItems);
    case LineItemType.Equipment:
      return Object.values(EquipmentItems);
    default:
      return [];
  }
};

const calculateCost = (lineItem: LineItem): number => {
  const units = parseFloat(lineItem.units) || 0;
  const time = parseFloat(lineItem.time) || 0;
  const rate = parseFloat(lineItem.rate) || 0;

  if (
    lineItem.type === LineItemType.Labor ||
    lineItem.type === LineItemType.Equipment
  ) {
    return units * time * rate;
  } else if (lineItem.type === LineItemType.Materials) {
    return units * rate;
  }
  return 0;
};

const calculatePrice = (lineItem: LineItem): number => {
  const cost = calculateCost(lineItem);
  const margin = parseFloat(lineItem.margin) || 0;

  if (margin >= 100) return cost; // Prevent division by zero or negative
  return cost / (1 - margin / 100);
};

const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount);
};

export default function EstimateForm({
  onSubmit,
  initialValues,
  isLoading = false,
}: Props) {
  const modal = useModal();

  const defaultInitialValues: FormValues = {
    lineItems: [
      {
        type: '',
        item: '',
        units: '',
        time: '',
        rate: '',
        margin: '',
      },
    ],
  };

  return (
    <Formik
      initialValues={initialValues || defaultInitialValues}
      validate={validateForm}
      validateOnChange={false}
      validateOnBlur={false}
      onSubmit={(values, { setSubmitting, setErrors, setTouched }) => {
        const validationErrors = validateForm(values);
        if (Object.keys(validationErrors).length > 0) {
          setErrors(validationErrors);
          // Mark all fields as touched to show errors
          const touchedFields: any = {
            lineItems: values.lineItems.map(() => ({
              type: true,
              item: true,
              units: true,
              time: true,
              rate: true,
              margin: true,
            })),
          };
          setTouched(touchedFields);
          setSubmitting(false);
          return;
        }
        onSubmit(values);
        setSubmitting(false);
      }}
    >
      {({
        values,
        errors,
        touched,
        handleSubmit,
        setFieldValue,
        isSubmitting,
        isValid,
      }) => (
        <form
          onSubmit={handleSubmit}
          onKeyDown={(event: any) => {
            if (event.key === 'Escape') {
              modal.dismiss();
            }
          }}
          style={{ width: '100%' }}
        >
          {/* Error Message at Top */}
          {(errors.lineItems || Object.keys(errors).length > 0) &&
            touched.lineItems && (
              <Box marginBottom="20px">
                <Paper
                  variant="outlined"
                  sx={{
                    padding: 2,
                    backgroundColor: '#ffebee',
                    borderColor: '#f44336',
                    borderWidth: 1,
                  }}
                >
                  <Typography color="error" variant="body2" fontWeight="medium">
                    Please fix the following errors:
                  </Typography>
                  <Box
                    component="ul"
                    sx={{ margin: '8px 0 0 20px', padding: 0 }}
                  >
                    {typeof errors.lineItems === 'string' && (
                      <Typography component="li" color="error" variant="body2">
                        {errors.lineItems}
                      </Typography>
                    )}
                    {Array.isArray(errors.lineItems) &&
                      errors.lineItems.map((lineError: any, index: number) => {
                        if (!lineError || Object.keys(lineError).length === 0)
                          return null;
                        return (
                          <Typography
                            key={index}
                            component="li"
                            color="error"
                            variant="body2"
                          >
                            Line {index + 1}:{' '}
                            {Object.values(lineError).join(', ')}
                          </Typography>
                        );
                      })}
                  </Box>
                </Paper>
              </Box>
            )}

          <Box marginBottom="20px">
            <Button startIcon={<AddIcon />} onClick={() => {}}>
              Add Customer
            </Button>
            <Typography variant="h6" gutterBottom>
              Estimate Line Items
            </Typography>

            <FieldArray name="lineItems">
              {({ push, remove }) => (
                <Box>
                  <TableContainer component={Paper} variant="outlined">
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell sx={{ width: '170px' }}>Type</TableCell>
                          <TableCell sx={{ width: '160px' }}>Item</TableCell>
                          <TableCell sx={{ width: '80px' }}>Units</TableCell>
                          <TableCell sx={{ width: '80px' }}>Time</TableCell>
                          <TableCell sx={{ width: '80px' }}>Rate</TableCell>
                          <TableCell sx={{ width: '100px' }}>Cost</TableCell>
                          <TableCell sx={{ width: '80px' }}>Margin</TableCell>
                          <TableCell sx={{ width: '100px' }}>Price</TableCell>
                          <TableCell sx={{ width: '80px' }}></TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {values.lineItems.map((lineItem, index) => {
                          const lineItemErrors: LineItemErrors = Array.isArray(
                            errors.lineItems,
                          )
                            ? (errors.lineItems[index] as LineItemErrors) || {}
                            : {};
                          const lineItemTouched = Array.isArray(
                            touched.lineItems,
                          )
                            ? touched.lineItems[index] || {}
                            : {};
                          const cost = calculateCost(lineItem);
                          const price = calculatePrice(lineItem);

                          return (
                            <TableRow key={index}>
                              {/* Type Dropdown */}
                              <TableCell sx={{ width: '170px' }}>
                                <FormControl
                                  size="small"
                                  fullWidth
                                  error={
                                    !!(
                                      lineItemErrors?.type &&
                                      lineItemTouched?.type
                                    )
                                  }
                                >
                                  <Select
                                    value={lineItem.type}
                                    onChange={(e) => {
                                      setFieldValue(
                                        `lineItems.${index}.type`,
                                        e.target.value,
                                      );
                                      // Reset item when type changes
                                      setFieldValue(
                                        `lineItems.${index}.item`,
                                        '',
                                      );
                                    }}
                                    displayEmpty
                                  >
                                    <MenuItem value="">
                                      <em>Select Type</em>
                                    </MenuItem>
                                    {Object.values(LineItemType).map((type) => (
                                      <MenuItem key={type} value={type}>
                                        {type}
                                      </MenuItem>
                                    ))}
                                  </Select>
                                </FormControl>
                              </TableCell>

                              {/* Item Dropdown */}
                              <TableCell sx={{ width: '160px' }}>
                                <FormControl
                                  size="small"
                                  fullWidth
                                  error={
                                    !!(
                                      lineItemErrors?.item &&
                                      lineItemTouched?.item
                                    )
                                  }
                                >
                                  <Select
                                    value={lineItem.item}
                                    onChange={(e) =>
                                      setFieldValue(
                                        `lineItems.${index}.item`,
                                        e.target.value,
                                      )
                                    }
                                    displayEmpty
                                    disabled={!lineItem.type}
                                  >
                                    <MenuItem value="">
                                      <em>Select Item</em>
                                    </MenuItem>
                                    {getItemOptions(
                                      lineItem.type as LineItemType,
                                    ).map((item) => (
                                      <MenuItem key={item} value={item}>
                                        {item}
                                      </MenuItem>
                                    ))}
                                  </Select>
                                </FormControl>
                              </TableCell>

                              {/* Units */}
                              <TableCell sx={{ width: '80px' }}>
                                <TextField
                                  size="small"
                                  fullWidth
                                  value={lineItem.units}
                                  onChange={(e) =>
                                    setFieldValue(
                                      `lineItems.${index}.units`,
                                      e.target.value,
                                    )
                                  }
                                  placeholder={
                                    lineItem.type === LineItemType.Labor
                                      ? 'e.g., 10 crew'
                                      : lineItem.type === LineItemType.Materials
                                        ? 'e.g., 10 tons'
                                        : lineItem.type ===
                                            LineItemType.Equipment
                                          ? 'e.g., 3'
                                          : 'Units'
                                  }
                                  error={
                                    !!(
                                      lineItemErrors?.units &&
                                      lineItemTouched?.units
                                    )
                                  }
                                />
                              </TableCell>

                              {/* Time (only for Labor and Equipment) */}
                              <TableCell sx={{ width: '80px' }}>
                                {lineItem.type === LineItemType.Labor ||
                                lineItem.type === LineItemType.Equipment ? (
                                  <TextField
                                    size="small"
                                    fullWidth
                                    type="number"
                                    value={lineItem.time}
                                    onChange={(e) =>
                                      setFieldValue(
                                        `lineItems.${index}.time`,
                                        e.target.value,
                                      )
                                    }
                                    placeholder="Hours"
                                    error={
                                      !!(
                                        lineItemErrors?.time &&
                                        lineItemTouched?.time
                                      )
                                    }
                                  />
                                ) : null}
                              </TableCell>

                              {/* Rate */}
                              <TableCell sx={{ width: '80px' }}>
                                <TextField
                                  size="small"
                                  fullWidth
                                  type="number"
                                  value={lineItem.rate}
                                  onChange={(e) =>
                                    setFieldValue(
                                      `lineItems.${index}.rate`,
                                      e.target.value,
                                    )
                                  }
                                  placeholder="0.00"
                                  InputProps={{
                                    startAdornment: '$',
                                  }}
                                  error={
                                    !!(
                                      lineItemErrors?.rate &&
                                      lineItemTouched?.rate
                                    )
                                  }
                                />
                              </TableCell>

                              {/* Cost (calculated) */}
                              <TableCell sx={{ width: '80px' }}>
                                <Typography variant="body2" fontWeight="medium">
                                  {formatCurrency(cost)}
                                </Typography>
                              </TableCell>

                              {/* Margin */}
                              <TableCell sx={{ width: '80px' }}>
                                <TextField
                                  size="small"
                                  fullWidth
                                  type="number"
                                  value={lineItem.margin}
                                  onChange={(e) =>
                                    setFieldValue(
                                      `lineItems.${index}.margin`,
                                      e.target.value,
                                    )
                                  }
                                  placeholder="0"
                                  InputProps={{
                                    endAdornment: '%',
                                  }}
                                  error={
                                    !!(
                                      lineItemErrors?.margin &&
                                      lineItemTouched?.margin
                                    )
                                  }
                                />
                              </TableCell>

                              {/* Price (calculated) */}
                              <TableCell sx={{ width: '100px' }}>
                                <Typography
                                  variant="body2"
                                  fontWeight="medium"
                                  color="primary"
                                >
                                  {formatCurrency(price)}
                                </Typography>
                              </TableCell>

                              {/* Actions */}
                              <TableCell sx={{ width: '80px' }}>
                                <IconButton
                                  size="small"
                                  onClick={() => remove(index)}
                                  disabled={values.lineItems.length === 1}
                                  color="error"
                                >
                                  <DeleteIcon />
                                </IconButton>
                              </TableCell>
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                  </TableContainer>

                  {/* Add Line Item Button */}
                  <Box marginTop="16px">
                    <Button
                      variant="outlined"
                      startIcon={<AddIcon />}
                      onClick={() =>
                        push({
                          type: '',
                          item: '',
                          units: '',
                          time: '',
                          rate: '',
                          margin: '',
                        })
                      }
                    >
                      Add Line Item
                    </Button>
                  </Box>
                </Box>
              )}
            </FieldArray>
          </Box>

          {/* Summary */}
          <Box marginBottom="20px">
            <Paper variant="outlined" sx={{ padding: 2 }}>
              <Typography variant="h6" gutterBottom>
                Summary
              </Typography>
              <Box
                display="flex"
                justifyContent="space-between"
                marginBottom="8px"
              >
                <Typography>Total Cost:</Typography>
                <Typography fontWeight="medium">
                  {formatCurrency(
                    values.lineItems.reduce(
                      (sum, item) => sum + calculateCost(item),
                      0,
                    ),
                  )}
                </Typography>
              </Box>
              <Box display="flex" justifyContent="space-between">
                <Typography>Total Price:</Typography>
                <Typography fontWeight="medium" color="primary">
                  {formatCurrency(
                    values.lineItems.reduce(
                      (sum, item) => sum + calculatePrice(item),
                      0,
                    ),
                  )}
                </Typography>
              </Box>
            </Paper>
          </Box>

          <FormActions
            label="Save Estimate"
            onSubmit={() => handleSubmit()}
            isDisabled={!isValid || isSubmitting || isLoading}
            isLoading={isSubmitting || isLoading}
          />
        </form>
      )}
    </Formik>
  );
}
