import { useMutation, useQueryClient, useQuery } from 'react-query';
import useProjectClient from '../../hooks/useProjectClient';

// Types for the estimate data (matching EstimateForm types)
export interface LineItemData {
  type: 'Labor' | 'Materials' | 'Equipment' | '';
  item: string;
  units: string;
  time: string;
  rate: string;
  margin: string;
}

export interface EstimateFormData {
  lineItems: LineItemData[];
}

export interface CreateEstimatePayload {
  estimate: {
    name?: string;
    description?: string;
  };
  lineItems: LineItemData[];
}

interface CreateEstimateMutationProps {
  payload: CreateEstimatePayload;
}

interface UpdateEstimateMutationProps {
  id: number;
  payload: CreateEstimatePayload;
}

interface DeleteEstimateMutationProps {
  id: number;
}

// Hook to create a new estimate
export function useCreateEstimate() {
  const client = useProjectClient();
  const queryClient = useQueryClient();

  return useMutation(
    async ({ payload }: CreateEstimateMutationProps) => {
      return client.createEstimate(payload);
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries('get-estimates');
      },
      onError: (error) => {
        console.error('Error creating estimate:', error);
      },
    },
  );
}

// Hook to get all estimates
export function useEstimates() {
  const client = useProjectClient();

  return useQuery('get-estimates', () => client.getEstimates(), {
    onError: (error) => {
      console.error('Error fetching estimates:', error);
    },
  });
}

// Hook to get a single estimate by ID
export function useEstimate(id: number) {
  const client = useProjectClient();

  return useQuery(['get-estimate', id], () => client.getEstimate(id), {
    enabled: !!id,
    onError: (error) => {
      console.error('Error fetching estimate:', error);
    },
  });
}

// Hook to update an estimate
export function useUpdateEstimate() {
  const client = useProjectClient();
  const queryClient = useQueryClient();

  return useMutation(
    async ({ id, payload }: UpdateEstimateMutationProps) => {
      return client.updateEstimate(id, payload);
    },
    {
      onSuccess: (data, variables) => {
        queryClient.invalidateQueries('get-estimates');
        queryClient.invalidateQueries(['get-estimate', variables.id]);
      },
      onError: (error) => {
        console.error('Error updating estimate:', error);
      },
    },
  );
}

// Hook to delete an estimate
export function useDeleteEstimate() {
  const client = useProjectClient();
  const queryClient = useQueryClient();

  return useMutation(
    async ({ id }: DeleteEstimateMutationProps) => {
      return client.deleteEstimate(id);
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries('get-estimates');
      },
      onError: (error) => {
        console.error('Error deleting estimate:', error);
      },
    },
  );
}
