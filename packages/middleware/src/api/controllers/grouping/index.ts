import { GroupingInput } from '../../../db/models/Grouping';
import * as service from '../../../db/services/GroupingService';
import { Grouping } from '../../interfaces';

export const getAll = async (): Promise<Grouping[]> => {
  return await service.getAll();
};

export const getPaginated = async (
  page: number,
  limit: number,
  order: string,
  orderBy: string,
) => {
  return await service.getPaginated(page, limit, order, orderBy);
};

export const getCountsBySeverity = async () => {
  return await service.getCountsBySeverity();
};

export const create = async (payload: any): Promise<Grouping> => {
  return await service.create(payload);
};

export const update = async (id: number, payload: Partial<GroupingInput>) => {
  return await service.update(id, payload);
};
