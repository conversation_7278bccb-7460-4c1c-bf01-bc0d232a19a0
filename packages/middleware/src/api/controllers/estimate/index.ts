import { EstimateInput, LineItemInput } from '../../../db/models/Estimate';
import * as service from '../../../db/services/EstimateService';
import { Estimate } from '../../interfaces';

export const getAll = async (): Promise<Estimate[]> => {
  return await service.getAll();
};

export const getById = async (id: number): Promise<Estimate | null> => {
  return await service.getById(id);
};

export const create = async (payload: {
  estimate: EstimateInput;
  lineItems: LineItemInput[];
}): Promise<Estimate> => {
  return await service.create(payload.estimate, payload.lineItems);
};

export const update = async (
  id: number,
  payload: {
    estimate?: Partial<EstimateInput>;
    lineItems?: LineItemInput[];
  }
): Promise<Estimate> => {
  return await service.update(id, payload.estimate || {}, payload.lineItems);
};

export const deleteById = async (id: number): Promise<boolean> => {
  return await service.deleteById(id);
};
