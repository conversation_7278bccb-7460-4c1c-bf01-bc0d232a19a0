import { Request, Response } from 'express';
import * as customerService from '../../../db/services/CustomerService';

export const createCustomer = async (req: Request, res: Response) => {
  try {
    const customer = await customerService.createCustomer(req.body);
    res.status(201).json(customer);
  } catch (error: any) {
    console.error('Error creating customer:', error);
    res.status(400).json({ 
      error: 'Failed to create customer',
      message: error.message 
    });
  }
};

export const getAllCustomers = async (req: Request, res: Response) => {
  try {
    const { search } = req.query;
    
    let customers;
    if (search && typeof search === 'string') {
      customers = await customerService.searchCustomers(search);
    } else {
      customers = await customerService.getAllCustomers();
    }
    
    res.json(customers);
  } catch (error: any) {
    console.error('Error fetching customers:', error);
    res.status(500).json({ 
      error: 'Failed to fetch customers',
      message: error.message 
    });
  }
};

export const getCustomerById = async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    const customer = await customerService.getCustomerById(id);
    
    if (!customer) {
      return res.status(404).json({ error: 'Customer not found' });
    }
    
    res.json(customer);
  } catch (error: any) {
    console.error('Error fetching customer:', error);
    res.status(400).json({ 
      error: 'Failed to fetch customer',
      message: error.message 
    });
  }
};

export const updateCustomer = async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    const customer = await customerService.updateCustomer(id, req.body);
    
    if (!customer) {
      return res.status(404).json({ error: 'Customer not found' });
    }
    
    res.json(customer);
  } catch (error: any) {
    console.error('Error updating customer:', error);
    res.status(400).json({ 
      error: 'Failed to update customer',
      message: error.message 
    });
  }
};

export const deleteCustomer = async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    const deleted = await customerService.deleteCustomer(id);
    
    if (!deleted) {
      return res.status(404).json({ error: 'Customer not found' });
    }
    
    res.status(204).send();
  } catch (error: any) {
    console.error('Error deleting customer:', error);
    res.status(400).json({ 
      error: 'Failed to delete customer',
      message: error.message 
    });
  }
};
