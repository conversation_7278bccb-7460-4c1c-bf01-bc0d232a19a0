import { Router, Request, Response } from 'express';
import * as groupingController from '../controllers/grouping';
import { GroupingInput } from '../../db/models/Grouping';

const groupingsRouter = Router();

groupingsRouter.get('/', async (req: Request, res: Response) => {
  const results = await groupingController.getAll();
  return res.status(200).send(results);
});

groupingsRouter.get('/paginate', async (req: Request, res: Response) => {
  const page = Number(req.query.page);
  const limit = Number(req.query.limit);
  const order = String(req.query.order);
  const orderBy = String(req.query.orderBy);
  const results = await groupingController.getPaginated(
    page,
    limit,
    order,
    orderBy,
  );
  return res.status(200).send(results);
});

groupingsRouter.get('/severity/counts', async (req: Request, res: Response) => {
  const results = await groupingController.getCountsBySeverity();
  return res.status(200).send(results);
});

groupingsRouter.post('/', async (req: Request, res: Response) => {
  const payload = req.body;
  const result = await groupingController.create(payload);
  return res.status(200).send(result);
});

groupingsRouter.patch('/:id', async (req: Request, res: Response) => {
  const id = Number(req.params.id);
  const payload: Partial<GroupingInput> = req.body;
  console.log({ payload });
  const result = await groupingController.update(id, payload);
  return res.status(201).send(result);
});

export default groupingsRouter;
