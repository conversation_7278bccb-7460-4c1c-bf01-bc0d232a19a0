import { Router } from 'express';

import findingsRouter from './findings';
import groupingsRouter from './groupings';
import estimatesRouter from './estimates';
import customersRouter from './customers';

const router = Router();

router.use('/findings', findingsRouter);
router.use('/groupings', groupingsRouter);
router.use('/estimates', estimatesRouter);
router.use('/customers', customersRouter);

export default router;
