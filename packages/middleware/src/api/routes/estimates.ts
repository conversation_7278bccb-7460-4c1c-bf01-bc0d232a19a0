import { Router, Request, Response } from 'express';
import * as estimateController from '../controllers/estimate';
import { EstimateInput, LineItemInput } from '../../db/models/Estimate';

const estimatesRouter = Router();

// GET /api/v1/estimates - Get all estimates
estimatesRouter.get('/', async (req: Request, res: Response) => {
  try {
    const results = await estimateController.getAll();
    return res.status(200).json(results);
  } catch (error) {
    console.error('Error fetching estimates:', error);
    return res.status(500).json({ 
      error: 'Failed to fetch estimates',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/v1/estimates/:id - Get estimate by ID
estimatesRouter.get('/:id', async (req: Request, res: Response) => {
  try {
    const id = Number(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({ error: 'Invalid estimate ID' });
    }

    const result = await estimateController.getById(id);
    if (!result) {
      return res.status(404).json({ error: 'Estimate not found' });
    }

    return res.status(200).json(result);
  } catch (error) {
    console.error('Error fetching estimate:', error);
    return res.status(500).json({ 
      error: 'Failed to fetch estimate',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /api/v1/estimates - Create new estimate
estimatesRouter.post('/', async (req: Request, res: Response) => {
  try {
    const { estimate, lineItems } = req.body;

    // Basic validation
    if (!lineItems || !Array.isArray(lineItems) || lineItems.length === 0) {
      return res.status(400).json({ 
        error: 'Line items are required and must be a non-empty array' 
      });
    }

    const payload = {
      estimate: estimate || {},
      lineItems: lineItems as LineItemInput[]
    };

    const result = await estimateController.create(payload);
    return res.status(201).json(result);
  } catch (error) {
    console.error('Error creating estimate:', error);
    return res.status(400).json({ 
      error: 'Failed to create estimate',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// PUT /api/v1/estimates/:id - Update estimate
estimatesRouter.put('/:id', async (req: Request, res: Response) => {
  try {
    const id = Number(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({ error: 'Invalid estimate ID' });
    }

    const { estimate, lineItems } = req.body;
    
    const payload = {
      estimate: estimate as Partial<EstimateInput>,
      lineItems: lineItems as LineItemInput[]
    };

    const result = await estimateController.update(id, payload);
    return res.status(200).json(result);
  } catch (error) {
    console.error('Error updating estimate:', error);
    return res.status(400).json({ 
      error: 'Failed to update estimate',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// DELETE /api/v1/estimates/:id - Delete estimate
estimatesRouter.delete('/:id', async (req: Request, res: Response) => {
  try {
    const id = Number(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({ error: 'Invalid estimate ID' });
    }

    await estimateController.deleteById(id);
    return res.status(204).send();
  } catch (error) {
    console.error('Error deleting estimate:', error);
    return res.status(400).json({ 
      error: 'Failed to delete estimate',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default estimatesRouter;
