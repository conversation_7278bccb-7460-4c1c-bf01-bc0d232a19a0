import { Router, Request, Response } from 'express';
import * as findingController from '../controllers/finding';

const findingsRouter = Router();

findingsRouter.get(
  '/:groupedFindingId',
  async (req: Request, res: Response) => {
    const groupedFindingId = Number(req.params.groupedFindingId);
    const results = await findingController.getByGroupId(groupedFindingId);
    return res.status(200).send(results);
  },
);

export default findingsRouter;
