import { Router } from 'express';
import * as customerController from '../controllers/customer';

const router = Router();

// GET /api/v1/customers - Get all customers (with optional search)
router.get('/', customerController.getAllCustomers);

// GET /api/v1/customers/:id - Get customer by ID
router.get('/:id', customerController.getCustomerById);

// POST /api/v1/customers - Create new customer
router.post('/', customerController.createCustomer);

// PUT /api/v1/customers/:id - Update customer
router.put('/:id', customerController.updateCustomer);

// DELETE /api/v1/customers/:id - Delete customer
router.delete('/:id', customerController.deleteCustomer);

export default router;
