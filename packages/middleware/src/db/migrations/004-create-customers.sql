-- Create customers table
CREATE TABLE IF NOT EXISTS customers (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  email TEXT,
  cc_bcc TEXT,
  billing_street_address_1 TEXT,
  billing_street_address_2 TEXT,
  billing_city TEXT,
  billing_state TEXT,
  billing_zip_code TEXT,
  billing_country TEXT,
  shipping_street_address_1 TEXT,
  shipping_street_address_2 TEXT,
  shipping_city TEXT,
  shipping_state TEXT,
  shipping_zip_code TEXT,
  shipping_country TEXT,
  same_as_billing_address BOOLEAN NOT NULL DEFAULT 0,
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create index on name for faster searches
CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(name);

-- Create index on email for faster searches
CREATE INDEX IF NOT EXISTS idx_customers_email ON customers(email);

-- Add customer_id column to estimates table
ALTER TABLE estimates ADD COLUMN customer_id INTEGER REFERENCES customers(id);

-- Create index on customer_id in estimates table
CREATE INDEX IF NOT EXISTS idx_estimates_customer_id ON estimates(customer_id);
