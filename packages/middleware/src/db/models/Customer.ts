import { DataTypes, Model, Optional } from 'sequelize';
import sequelizeConnection from '../config';

interface CustomerAttributes {
  id: number;
  name: string;
  email?: string;
  ccBcc?: string;
  billingStreetAddress1?: string;
  billingStreetAddress2?: string;
  billingCity?: string;
  billingState?: string;
  billingZipCode?: string;
  billingCountry?: string;
  shippingStreetAddress1?: string;
  shippingStreetAddress2?: string;
  shippingCity?: string;
  shippingState?: string;
  shippingZipCode?: string;
  shippingCountry?: string;
  sameAsBillingAddress: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface CustomerInput extends Optional<CustomerAttributes, 'id' | 'createdAt' | 'updatedAt'> {}
export interface CustomerOutput extends CustomerAttributes {}

class Customer extends Model<CustomerAttributes, CustomerInput> implements CustomerAttributes {
  id!: number;
  name!: string;
  email?: string;
  ccBcc?: string;
  billingStreetAddress1?: string;
  billingStreetAddress2?: string;
  billingCity?: string;
  billingState?: string;
  billingZipCode?: string;
  billingCountry?: string;
  shippingStreetAddress1?: string;
  shippingStreetAddress2?: string;
  shippingCity?: string;
  shippingState?: string;
  shippingZipCode?: string;
  shippingCountry?: string;
  sameAsBillingAddress!: boolean;
  createdAt!: Date;
  updatedAt!: Date;
}

Customer.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    email: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    ccBcc: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'cc_bcc',
    },
    billingStreetAddress1: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'billing_street_address_1',
    },
    billingStreetAddress2: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'billing_street_address_2',
    },
    billingCity: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'billing_city',
    },
    billingState: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'billing_state',
    },
    billingZipCode: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'billing_zip_code',
    },
    billingCountry: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'billing_country',
    },
    shippingStreetAddress1: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'shipping_street_address_1',
    },
    shippingStreetAddress2: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'shipping_street_address_2',
    },
    shippingCity: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'shipping_city',
    },
    shippingState: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'shipping_state',
    },
    shippingZipCode: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'shipping_zip_code',
    },
    shippingCountry: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'shipping_country',
    },
    sameAsBillingAddress: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      field: 'same_as_billing_address',
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at',
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at',
    },
  },
  {
    sequelize: sequelizeConnection,
    timestamps: true,
    tableName: 'customers',
  }
);

export default Customer;
