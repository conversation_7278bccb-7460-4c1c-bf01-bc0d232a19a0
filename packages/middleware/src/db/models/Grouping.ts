import { DataTypes, Model, Optional } from 'sequelize';
import sequelizeConnection from '../config';

interface GroupingAttributes {
  id: number;
  groupingType: string;
  groupingKey: string;
  severity: string;
  groupedFindingCreated: string;
  sla: string;
  description: string;
  securityAnalyst: string;
  owner: string;
  workflow: string;
  statusStr: string;
  progress: number;
}

export interface GroupingInput extends Optional<GroupingAttributes, 'id'> {}

export interface GroupingOutput extends Required<GroupingAttributes> {}

interface PaginatedGroupingAttributes {
  count: number;
  rows: GroupingAttributes[];
}

export interface PaginatedGroupingOutput
  extends Required<PaginatedGroupingAttributes> {}

class Grouping
  extends Model<GroupingAttributes, any>
  implements GroupingAttributes
{
  id!: number;
  groupingType!: string;
  groupingKey!: string;
  severity!: string;
  groupedFindingCreated!: string;
  sla!: string;
  description!: string;
  securityAnalyst!: string;
  owner!: string;
  workflow!: string;
  statusStr!: string;
  progress!: number;
}

Grouping.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    groupingType: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'grouping_type',
    },
    groupingKey: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'grouping_key',
    },
    severity: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'severity',
    },
    groupedFindingCreated: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'grouped_finding_created',
    },
    sla: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'sla',
    },
    description: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'description',
    },
    securityAnalyst: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'security_analyst',
    },
    owner: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'owner',
    },
    workflow: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'workflow',
    },
    statusStr: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'status',
    },
    progress: {
      type: DataTypes.FLOAT,
      allowNull: false,
      field: 'progress',
    },
  },
  {
    sequelize: sequelizeConnection,
    //NOTE: soft delete on the model by adding a deletedAt attribute
    // that marks records as deleted when invoking the destroy method
    paranoid: true,
    timestamps: false,
    tableName: 'grouped_findings',
  },
);

export default Grouping;
