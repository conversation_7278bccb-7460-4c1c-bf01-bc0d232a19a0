import { DataTypes, Model } from 'sequelize';
import sequelizeConnection from '../config';

interface FindingAttributes {
  id: number;
  sourceSecurityToolName: string;
  sourceSecurityToolId: string;
  sourceCollaborationToolName: string;
  sourceCollaborationToolId: string;
  severity: string;
  createdAt: string;
  ticketCreated: string;
  description: string;
  asset: string;
  statusStr: string;
  remediationUrl: string;
  remediationText: string;
  groupedFindingId: number;
}

export interface FindingOutput extends Required<FindingAttributes> {}

class Finding
  extends Model<FindingAttributes, any>
  implements FindingAttributes
{
  id!: number;
  sourceSecurityToolName!: string;
  sourceSecurityToolId!: string;
  sourceCollaborationToolName!: string;
  sourceCollaborationToolId!: string;
  severity!: string;
  createdAt!: string;
  ticketCreated!: string;
  description!: string;
  asset!: string;
  statusStr!: string;
  remediationUrl!: string;
  remediationText!: string;
  groupedFindingId!: number;
}

Finding.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    sourceSecurityToolName: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'source_security_tool_name',
    },
    sourceSecurityToolId: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'source_security_tool_id',
    },
    sourceCollaborationToolName: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'source_collaboration_tool_name',
    },
    sourceCollaborationToolId: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'source_collaboration_tool_id',
    },
    severity: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'severity',
    },
    createdAt: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'finding_created',
    },
    ticketCreated: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'ticket_created',
    },
    description: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'description',
    },
    asset: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'asset',
    },
    statusStr: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'status',
    },
    remediationUrl: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'remediation_url',
    },
    remediationText: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'remediation_text',
    },
    groupedFindingId: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'grouped_finding_id',
    },
  },
  {
    sequelize: sequelizeConnection,
    //NOTE: soft delete on the model by adding a deletedAt attribute
    // that marks records as deleted when invoking the destroy method
    paranoid: true,
    timestamps: false,
    tableName: 'raw_findings',
  },
);

export default Finding;
