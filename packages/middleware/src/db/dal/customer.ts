import Customer, { CustomerInput, CustomerOutput } from '../models/Customer';

export const create = async (payload: CustomerInput): Promise<CustomerOutput> => {
  const customer = await Customer.create(payload);
  return customer;
};

export const getAll = async (): Promise<CustomerOutput[]> => {
  return Customer.findAll({
    order: [['name', 'ASC']],
  });
};

export const getById = async (id: number): Promise<CustomerOutput | null> => {
  return Customer.findByPk(id);
};

export const update = async (id: number, payload: Partial<CustomerInput>): Promise<CustomerOutput | null> => {
  const customer = await Customer.findByPk(id);
  if (!customer) {
    return null;
  }
  
  await customer.update(payload);
  return customer.reload();
};

export const deleteById = async (id: number): Promise<boolean> => {
  const deletedCount = await Customer.destroy({
    where: { id },
  });
  return deletedCount > 0;
};

export const searchByName = async (searchTerm: string): Promise<CustomerOutput[]> => {
  return Customer.findAll({
    where: {
      name: {
        [require('sequelize').Op.iLike]: `%${searchTerm}%`,
      },
    },
    order: [['name', 'ASC']],
    limit: 10,
  });
};
