import sequelize from 'sequelize';
import { Grouping } from '../models';
import {
  GroupingInput,
  GroupingOutput,
  PaginatedGroupingOutput,
} from '../models/Grouping';

export const getAll = async (): Promise<GroupingOutput[]> => {
  return Grouping.findAll();
};

export const getPaginated = async (
  page: number,
  limit: number,
  order: string,
  orderBy: string,
): Promise<PaginatedGroupingOutput> => {
  return Grouping.findAndCountAll({
    offset: limit * page,
    limit,
    order: [[orderBy, order]],
  });
};

export const getCountsBySeverity = async () => {
  return Grouping.findAll({
    attributes: [
      'severity',
      [sequelize.fn('count', sequelize.col('severity')), 'count'],
    ],
    order: [['count', 'DESC']],
    group: ['severity'],
  });
};

export const create = async (
  payload: GroupingInput,
): Promise<GroupingOutput> => {
  const grouping = await Grouping.create(payload);
  return grouping;
};

export const update = async (
  id: number,
  payload: Partial<GroupingInput>,
): Promise<GroupingOutput> => {
  const grouping = await Grouping.findByPk(id);
  if (!grouping) {
    throw new Error('Not found');
  }
  console.log('update', payload);
  const updatedGrouping = await grouping.update(payload);

  return updatedGrouping;
};
