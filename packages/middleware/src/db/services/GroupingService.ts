import * as groupingDal from '../dal/grouping';
import {
  GroupingInput,
  GroupingOutput,
  PaginatedGroupingOutput,
} from '../models/Grouping';

export const getAll = (): Promise<GroupingOutput[]> => {
  return groupingDal.getAll();
};

export const getPaginated = (
  page: number,
  limit: number,
  order: string,
  orderBy: string,
): Promise<PaginatedGroupingOutput> => {
  return groupingDal.getPaginated(page, limit, order, orderBy);
};

export const getCountsBySeverity = () => {
  return groupingDal.getCountsBySeverity();
};

export const create = async (
  payload: GroupingInput,
): Promise<GroupingOutput> => {
  return groupingDal.create(payload);
};

export const update = async (
  id: number,
  payload: Partial<GroupingInput>,
): Promise<GroupingOutput> => {
  return groupingDal.update(id, payload);
};
