import * as estimateDal from '../dal/estimate';
import {
  EstimateInput,
  EstimateOutput,
  LineItemInput,
} from '../models/Estimate';

export const getAll = (): Promise<EstimateOutput[]> => {
  return estimateDal.getAll();
};

export const getById = (id: number): Promise<EstimateOutput | null> => {
  return estimateDal.getById(id);
};

export const create = async (
  estimateData: EstimateInput,
  lineItemsData: LineItemInput[]
): Promise<EstimateOutput> => {
  // Basic validation
  if (!lineItemsData || lineItemsData.length === 0) {
    throw new Error('At least one line item is required');
  }

  // Validate each line item
  for (const item of lineItemsData) {
    if (!item.type) {
      throw new Error('Line item type is required');
    }
    
    if (!item.item) {
      throw new Error('Line item name is required');
    }
    
    if (!item.units) {
      throw new Error('Line item units is required');
    }
    
    if ((item.type === 'Labor' || item.type === 'Equipment') && !item.time) {
      throw new Error('Time is required for Labor and Equipment items');
    }
    
    if (!item.rate || parseFloat(item.rate.toString()) <= 0) {
      throw new Error('Valid rate is required for all items');
    }
    
    if (item.margin === undefined || parseFloat(item.margin.toString()) < 0 || parseFloat(item.margin.toString()) >= 100) {
      throw new Error('Margin must be between 0 and 99');
    }
  }

  return estimateDal.create(estimateData, lineItemsData);
};

export const update = async (
  id: number,
  estimateData: Partial<EstimateInput>,
  lineItemsData?: LineItemInput[]
): Promise<EstimateOutput> => {
  // Validate line items if provided
  if (lineItemsData) {
    if (lineItemsData.length === 0) {
      throw new Error('At least one line item is required');
    }

    for (const item of lineItemsData) {
      if (!item.type) {
        throw new Error('Line item type is required');
      }
      
      if (!item.item) {
        throw new Error('Line item name is required');
      }
      
      if (!item.units) {
        throw new Error('Line item units is required');
      }
      
      if ((item.type === 'Labor' || item.type === 'Equipment') && !item.time) {
        throw new Error('Time is required for Labor and Equipment items');
      }
      
      if (!item.rate || parseFloat(item.rate.toString()) <= 0) {
        throw new Error('Valid rate is required for all items');
      }
      
      if (item.margin === undefined || parseFloat(item.margin.toString()) < 0 || parseFloat(item.margin.toString()) >= 100) {
        throw new Error('Margin must be between 0 and 99');
      }
    }
  }

  return estimateDal.update(id, estimateData, lineItemsData);
};

export const deleteById = async (id: number): Promise<boolean> => {
  return estimateDal.deleteById(id);
};
