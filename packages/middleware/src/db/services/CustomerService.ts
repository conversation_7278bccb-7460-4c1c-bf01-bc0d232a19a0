import * as customerDal from '../dal/customer';
import { CustomerInput, CustomerOutput } from '../models/Customer';

export const createCustomer = async (payload: CustomerInput): Promise<CustomerOutput> => {
  // Validate required fields
  if (!payload.name || payload.name.trim() === '') {
    throw new Error('Customer name is required');
  }

  // Validate email format if provided
  if (payload.email && payload.email.trim() !== '') {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(payload.email)) {
      throw new Error('Invalid email format');
    }
  }

  // If sameAsBillingAddress is true, copy billing address to shipping address
  if (payload.sameAsBillingAddress) {
    payload.shippingStreetAddress1 = payload.billingStreetAddress1;
    payload.shippingStreetAddress2 = payload.billingStreetAddress2;
    payload.shippingCity = payload.billingCity;
    payload.shippingState = payload.billingState;
    payload.shippingZipCode = payload.billingZipCode;
    payload.shippingCountry = payload.billingCountry;
  }

  return customerDal.create(payload);
};

export const getAllCustomers = async (): Promise<CustomerOutput[]> => {
  return customerDal.getAll();
};

export const getCustomerById = async (id: number): Promise<CustomerOutput | null> => {
  if (!id || id <= 0) {
    throw new Error('Valid customer ID is required');
  }

  return customerDal.getById(id);
};

export const updateCustomer = async (id: number, payload: Partial<CustomerInput>): Promise<CustomerOutput | null> => {
  if (!id || id <= 0) {
    throw new Error('Valid customer ID is required');
  }

  // Validate name if provided
  if (payload.name !== undefined && (!payload.name || payload.name.trim() === '')) {
    throw new Error('Customer name is required');
  }

  // Validate email format if provided
  if (payload.email && payload.email.trim() !== '') {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(payload.email)) {
      throw new Error('Invalid email format');
    }
  }

  // If sameAsBillingAddress is true, copy billing address to shipping address
  if (payload.sameAsBillingAddress) {
    payload.shippingStreetAddress1 = payload.billingStreetAddress1;
    payload.shippingStreetAddress2 = payload.billingStreetAddress2;
    payload.shippingCity = payload.billingCity;
    payload.shippingState = payload.billingState;
    payload.shippingZipCode = payload.billingZipCode;
    payload.shippingCountry = payload.billingCountry;
  }

  return customerDal.update(id, payload);
};

export const deleteCustomer = async (id: number): Promise<boolean> => {
  if (!id || id <= 0) {
    throw new Error('Valid customer ID is required');
  }

  return customerDal.deleteById(id);
};

export const searchCustomers = async (searchTerm: string): Promise<CustomerOutput[]> => {
  if (!searchTerm || searchTerm.trim() === '') {
    return getAllCustomers();
  }

  return customerDal.searchByName(searchTerm.trim());
};
