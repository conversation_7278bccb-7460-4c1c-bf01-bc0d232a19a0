require('dotenv').config();
import { Dialect, Sequelize } from 'sequelize';

const dbDriver = process.env.DB_DRIVER as Dialect;
const dbStorage = process.env.DB_STORAGE as string;

// Validate required environment variables
if (!dbDriver) {
  throw new Error('DB_DRIVER environment variable is required');
}

if (!dbStorage) {
  throw new Error('DB_STORAGE environment variable is required');
}

const sequelizeConnection = new Sequelize({
  dialect: dbDriver,
  storage: dbStorage,
  logging: process.env.NODE_ENV === 'development' ? console.log : false,
});

export default sequelizeConnection;
