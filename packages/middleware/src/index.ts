require('dotenv').config();
import express, { Application, Request, Response } from 'express';
import cors from 'cors';

import routes from './api/routes';

const port = process.env.PORT || 3000;

export const get = () => {
  const app: Application = express();
  // Body parsing Middleware
  app.use(cors());
  app.use(express.json());
  app.use(express.urlencoded({ extended: true }));
  app.get('/', async (req: Request, res: Response): Promise<Response> => {
    return res.status(200).send({
      message: `Welcome to the project API! \n Endpoints available at http://localhost:${port}/api/v1`,
    });
  });

  app.use('/api/v1', routes);

  return app;
};

export const start = () => {
  const app = get();
  try {
    app.listen(port, () => {
      console.log(`middleware ready at http://localhost:${port} 🚀`);
    });
  } catch (error: any) {
    console.log(`Error occurred: ${error.message}`);
  }
};

start();
