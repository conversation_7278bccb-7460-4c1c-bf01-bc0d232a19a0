## Mini ERP webapp

This project requires [Node.js][node] v21.4.0 and [Yarn][yarn] v1.22.21

#### Installation

**Node.js** You may install install using [Homebrew][homebrew] or [nvm][nvm].
For Homebrew. `node` can be installed by running the following command.

```shell
$ brew install node@21
```

**Yarn** Yarn can be installed through [Homebrew][homebrew] by running the
following command.

```shell
$ brew install yarn
```

<!-- LINKS -->

[node]: https://nodejs.org/
[yarn]: https://classic.yarnpkg.com/
[homebrew]: https://brew.sh/
[nvm]: https://nvm.sh
