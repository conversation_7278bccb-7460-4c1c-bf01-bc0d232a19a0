# Business Operations App

This project requires [Node.js][node] v21.4.0 and [Yarn][yarn] v1.22.21

#### Installation

**Node.js** You may install install using [Homebrew][homebrew] or [nvm][nvm].
For Homebrew. `node` can be installed by running the following command.

```shell
$ brew install node@21
```

**Yarn** Yarn can be installed through [Homebrew][homebrew] by running the
following command.

```shell
$ brew install yarn
```

## Scripts

### Root Scripts

- `clean`: Destroys all local dependency
- `dev`: Runs frontend and middleware packages

### Environment variables

Each package includes example file `.env.example`. It specifies `PORT` and other
settings for a project.

### Steps to run development environment

- copy `.env.example` to `.env.local` for frontend and middleware

```
cp packages/frontend/.env.example packages/frontend/.env.local
cp packages/middleware/.env.example packages/middleware/.env.local
```

- install packages and run dev server

```shell
yarn
yarn run dev
```

## Middleware

Sample data is located `/packages/middleware/data/`

## Screenshots

<!-- LINKS -->

[node]: https://nodejs.org/
[yarn]: https://classic.yarnpkg.com/
[homebrew]: https://brew.sh/
[nvm]: https://nvm.sh

Each estimate must have Add Customer dropdown. This dropdown will pull all
customers from a database and allow to add a new cutomer. Initially when we have
no customers, dropdown would only show plus icon "Add New". Once, we saved a
customer in database, dropdown would display "Add New" and a customer that we
added. When Add New is clicked, we should open a drawer. See attached screenshot
for fields to capture. Implement exactly as shown in screenshot. 2 expandable
sections: Name and contact, and Addresses. Make sure to create backend API
endpoints to support this functionality. Use existing code as example.
