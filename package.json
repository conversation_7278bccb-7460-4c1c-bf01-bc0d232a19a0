{"name": "project", "description": "Dashboard to display security fundings", "author": "<PERSON> <<EMAIL>>", "private": true, "version": "1.0.0", "license": "MIT", "scripts": {"build": "lerna run --stream build", "clean": "find ./ -name 'node_modules' -type d -exec rm -rf '{}' + && find ./packages -name 'build' -type d -exec rm -rf '{}' + &>/dev/null", "start": "echo 'Please run starts using: yarn workspace frontend start and yarn workspace middleware start'", "dev": "concurrently 'yarn workspace middleware run start' 'yarn workspace frontend run start'"}, "devDependencies": {"lerna": "^8.0.1", "prettier": "^3.1.1", "pretty-quick": "^3.1.3"}, "prettier": {"printWidth": 80, "tabWidth": 2, "useTabs": false, "semi": true, "singleQuote": true, "quoteProps": "consistent", "jsxSingleQuote": false, "trailingComma": "all", "bracketSpacing": true, "jsxBracketSameLine": false, "arrowParens": "always", "requirePragma": false, "proseWrap": "always"}, "workspaces": ["packages/*"], "dependencies": {"concurrently": "^8.2.2"}}